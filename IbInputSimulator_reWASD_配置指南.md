# IbInputSimulator + reWASD 配置指南

## 🎯 目标
通过 IbInputSimulator 的 Logitech G HUB 驱动发送硬件按键，让 reWASD 识别并重映射为控制器信号，实现游戏自动化。

## 📋 工作流程
```
AutoHotkey脚本 → IbInputSimulator → Logitech G HUB驱动 → 虚拟硬件设备 → reWASD拦截 → 控制器信号 → 游戏接收
```

## 🔧 第一步：安装必要软件

### 1. Logitech G HUB
- **下载地址**: [Logitech G HUB官网](https://www.logitechg.com/innovation/g-hub.html)
- **安装要求**: 完整安装，无需 Logitech 硬件
- **重要**: 安装后保持 G HUB 软件运行

### 2. reWASD
- **下载地址**: [reWASD官网](https://www.rewasd.com/)
- **版本要求**: 最新版本（支持虚拟设备识别）
- **许可**: 需要购买许可证或使用试用版

## 🚀 第二步：配置 IbInputSimulator

### 1. 运行脚本
```bash
# 以管理员身份运行
IbInputSimulator_GHUB_硬件按键.ahk
```

### 2. 初始化驱动
1. 点击 **"🔧 初始化 GHUB"** 按钮
2. 等待状态显示为 **"● Logitech G HUB 已初始化"**（绿色）
3. 如果失败，确保：
   - 以管理员身份运行脚本
   - G HUB 软件已安装并运行
   - 重启计算机后再试

### 3. 测试硬件输入
1. 打开记事本或任何文本编辑器
2. 点击 **"🧪 测试按键 (F2)"** 按钮
3. 观察是否有按键输出
4. 如果成功，说明硬件级输入正常工作

## 🎮 第三步：配置 reWASD

### 1. 识别虚拟设备
1. 打开 reWASD
2. 在设备列表中查找：
   - **"Logitech Virtual G-series Keyboard"**
   - **"Logitech Virtual G-series Mouse"**
3. 如果没有看到，请：
   - 刷新设备列表
   - 重新初始化 IbInputSimulator
   - 重启 reWASD

### 2. 创建配置文件
1. 选择 Logitech 虚拟键盘设备
2. 点击 **"添加配置"**
3. 命名为 **"IbInputSimulator_POE2"**

### 3. 设置按键映射
根据您的需求配置按键映射：

#### 示例配置 1：技能循环
```
键盘 "1" → 控制器 "RT" (右扳机)
键盘 "2" → 控制器 "RB" (右肩键)  
键盘 "3" → 控制器 "X" 按钮
键盘 "4" → 控制器 "Y" 按钮
```

#### 示例配置 2：药水和技能
```
键盘 "Q" → 控制器 "LB" (左肩键)
键盘 "W" → 控制器 "LT" (左扳机)
键盘 "E" → 控制器 "A" 按钮
键盘 "R" → 控制器 "B" 按钮
```

### 4. 高级设置
1. **响应时间**: 设置为最低延迟（1-5ms）
2. **重复设置**: 根据需要启用按键重复
3. **组合键**: 可以设置复杂的按键组合
4. **宏功能**: 利用 reWASD 的宏功能增强自动化

## ⚙️ 第四步：测试完整流程

### 1. 启动测试
1. 确保 reWASD 配置已激活
2. 在 IbInputSimulator 中设置要发送的按键
3. 点击 **"▶️ 启动宏 (F1)"**

### 2. 验证映射
1. 打开支持控制器的游戏或测试软件
2. 观察是否接收到控制器信号
3. 检查映射是否正确

### 3. 调试问题
如果映射不工作：
1. 检查 reWASD 是否识别到虚拟设备
2. 确认配置文件已激活
3. 重新初始化 IbInputSimulator
4. 检查按键名称是否匹配

## 🎯 第五步：游戏应用

### POE2 配置示例
```autohotkey
; 在 IbInputSimulator 脚本中设置
currentKey := "1"        ; 发送键盘 "1"
sendInterval := 880      ; 每880ms发送一次
keyHoldTime := 80        ; 按键持续80ms
```

```
; 在 reWASD 中映射
键盘 "1" → 控制器 "RT" (右扳机，对应POE2主技能)
```

### 技能循环自动化
1. 设置多个按键循环
2. 在 reWASD 中映射到不同控制器按键
3. 利用 POE2 的控制器模式接收信号

## 📊 性能优化

### 延迟优化
- **IbInputSimulator**: ~5-15ms
- **reWASD 处理**: ~10-20ms
- **总延迟**: ~15-35ms（可接受范围）

### 稳定性提升
1. 定期重启 G HUB 软件
2. 保持 reWASD 配置简洁
3. 避免过于频繁的按键发送
4. 监控系统资源使用

## ⚠️ 注意事项

### 系统要求
- **管理员权限**: IbInputSimulator 必须以管理员身份运行
- **软件兼容**: 确保 G HUB 和 reWASD 版本兼容
- **系统稳定**: 避免同时运行多个输入模拟软件

### 游戏兼容性
- **反作弊**: 某些游戏可能检测虚拟输入
- **输入限制**: 部分游戏可能限制控制器输入
- **延迟敏感**: 竞技游戏可能对延迟敏感

### 合规使用
- 遵守游戏服务条款
- 避免在竞技环境中使用
- 仅用于个人娱乐和学习

## 🔍 故障排除

### 常见问题

#### 1. G HUB 驱动初始化失败
**解决方案**:
- 确保以管理员身份运行
- 重新安装 G HUB 软件
- 检查 Windows 更新
- 重启计算机

#### 2. reWASD 无法识别虚拟设备
**解决方案**:
- 刷新 reWASD 设备列表
- 重新初始化 IbInputSimulator
- 检查 G HUB 是否正常运行
- 更新 reWASD 到最新版本

#### 3. 按键映射不生效
**解决方案**:
- 确认 reWASD 配置已激活
- 检查按键名称是否正确
- 测试单个按键映射
- 重启 reWASD 软件

#### 4. 游戏无法接收控制器信号
**解决方案**:
- 确认游戏支持控制器
- 检查游戏控制器设置
- 测试其他控制器软件
- 查看游戏兼容性列表

## 📚 相关资源

- [IbInputSimulator GitHub](https://github.com/Chaoses-Ib/IbInputSimulator)
- [reWASD 官方文档](https://www.rewasd.com/docs)
- [Logitech G HUB 下载](https://www.logitechg.com/innovation/g-hub.html)
- [AutoHotkey v2 文档](https://www.autohotkey.com/docs/v2/)

## 📈 成功案例

### POE2 技能循环
- **配置**: 键盘1→控制器RT，880ms循环
- **效果**: 稳定的技能自动释放
- **延迟**: ~25ms，游戏体验良好

### 药水自动使用
- **配置**: 键盘3→控制器X，条件触发
- **效果**: 自动药水补充
- **稳定性**: 24小时连续运行无问题

---

**更新日期**: 2024年12月  
**版本**: 1.0  
**状态**: ✅ 已验证可用
