#Requires AutoHotkey v2.0
#SingleInstance Force

; ===================================================================
; IbInputSimulator 快速测试
; 快速测试IbInputSimulator是否可以正常工作
; ===================================================================

; 测试结果
global TestResult := ""

; 快速测试函数
QuickTest() {
    global TestResult
    TestResult := ""
    
    ; 1. 检查基本环境
    TestResult .= "🔍 IbInputSimulator 快速测试`n"
    TestResult .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n"
    
    ; 检查AutoHotkey版本
    ahkBitness := A_PtrSize == 8 ? "64-bit" : "32-bit"
    TestResult .= "AutoHotkey: " . A_AhkVersion . " (" . ahkBitness . ")`n"
    
    if (ahkBitness != "64-bit") {
        TestResult .= "❌ 错误: 必须使用64位版本`n"
        ShowResult()
        return
    }
    
    ; 检查管理员权限
    TestResult .= "管理员权限: " . (A_IsAdmin ? "✅ 是" : "⚠️ 否") . "`n"
    
    ; 检查文件存在性
    dllPath := A_ScriptDir . "\IbInputSimulator\IbInputSimulator.dll"
    ahkPath := A_ScriptDir . "\IbInputSimulator\IbInputSimulator.ahk"
    
    dllExists := FileExist(dllPath) ? true : false
    ahkExists := FileExist(ahkPath) ? true : false
    
    TestResult .= "DLL文件: " . (dllExists ? "✅ 存在" : "❌ 不存在") . "`n"
    TestResult .= "AHK文件: " . (ahkExists ? "✅ 存在" : "❌ 不存在") . "`n"
    
    if (!dllExists || !ahkExists) {
        TestResult .= "❌ 错误: 缺少必要文件`n"
        ShowResult()
        return
    }
    
    ; 2. 尝试导入库
    TestResult .= "`n📚 导入测试:`n"
    try {
        #Include "IbInputSimulator\IbInputSimulator.ahk"
        TestResult .= "库导入: ✅ 成功`n"
        
        ; 检查函数
        if (IsSet(IbSendInit) && IsSet(IbSend) && IsSet(IbSendDestroy)) {
            TestResult .= "函数检查: ✅ 完整`n"
        } else {
            TestResult .= "函数检查: ❌ 不完整`n"
            ShowResult()
            return
        }
    } catch Error as e {
        TestResult .= "库导入: ❌ 失败 (" . e.Message . ")`n"
        ShowResult()
        return
    }
    
    ; 3. 测试最常用的驱动
    TestResult .= "`n🎮 驱动测试:`n"
    
    ; 测试SendInput (最兼容)
    try {
        IbSendInit("SendInput", 0)
        TestResult .= "SendInput: ✅ 成功`n"
        IbSendDestroy()
        
        ; 如果SendInput成功，进行按键测试
        TestResult .= "`n⌨️ 按键测试:`n"
        TestResult .= "准备测试按键发送...(3秒后开始)`n"
        ShowResult()
        
        ; 等待3秒
        Sleep(3000)
        
        ; 测试按键发送
        try {
            IbSendInit("SendInput", 1)
            TestResult .= "按键测试: 发送 'Hello' 到记事本`n"
            
            ; 打开记事本
            Run("notepad.exe")
            Sleep(1000)
            
            ; 发送测试文本
            IbSend("Hello IbInputSimulator!")
            
            TestResult .= "按键测试: ✅ 完成`n"
            TestResult .= "如果记事本中出现文本，说明工作正常`n"
            
            IbSendDestroy()
        } catch Error as e {
            TestResult .= "按键测试: ❌ 失败 (" . e.Message . ")`n"
        }
        
    } catch Error as e {
        TestResult .= "SendInput: ❌ 失败 (" . e.Message . ")`n"
        
        ; 尝试AnyDriver
        try {
            IbSendInit("AnyDriver", 0)
            TestResult .= "AnyDriver: ✅ 成功`n"
            IbSendDestroy()
        } catch Error as e2 {
            TestResult .= "AnyDriver: ❌ 失败 (" . e2.Message . ")`n"
            TestResult .= "`n❌ 所有基本驱动都失败了`n"
        }
    }
    
    TestResult .= "`n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n"
    TestResult .= "测试完成！`n"
    
    ShowResult()
}

; 显示结果
ShowResult() {
    global TestResult
    MsgBox(TestResult, "IbInputSimulator 快速测试结果", 0)
}

; 创建简单GUI
CreateTestGUI() {
    ; 创建GUI对象
    testGui := Gui("+AlwaysOnTop", "IbInputSimulator 快速测试")
    
    testGui.AddText("x10 y10 w280 h30 Center", "⚡ IbInputSimulator 快速测试")
    testGui.AddText("x10 y50 w280 h40", "快速检测 IbInputSimulator 是否可以正常工作")
    
    btnTest := testGui.AddButton("x10 y100 w100 h30", "开始测试")
    btnTest.OnEvent("Click", (*) => QuickTest())
    
    btnExit := testGui.AddButton("x120 y100 w100 h30", "退出")
    btnExit.OnEvent("Click", (*) => ExitApp())
    
    testGui.AddText("x10 y140 w280 h50", 
        "测试内容：`n" .
        "• 环境检查 • 库导入 • 驱动测试 • 按键测试")
    
    testGui.Show("w300 h200")
    return testGui
}

; 启动测试工具
CreateTestGUI()

; 热键
F1::QuickTest()
Esc::ExitApp() 