# POE2 IbInputSimulator 驱动级输入宏 - 使用说明

## 🚀 项目概述

这是一个使用 [IbInputSimulator](https://github.com/Chaoses-Ib/IbInputSimulator) 库的POE2宏，通过驱动程序发送输入信号，可能绕过POE2的输入限制。

**核心优势**:
- **驱动级输入**: 绕过Windows标准输入限制
- **多驱动支持**: Logitech、Razer、DD等多种选择
- **无需额外软件**: 不需要reWASD、InputMapper等中间件
- **直接输入**: 可能直接被POE2识别

## 📋 系统要求

- Windows 10/11
- AutoHotkey v2.0 (64位)
- 管理员权限
- 对应的驱动软件 (根据选择的驱动)

## 🔧 安装步骤

### 1. 准备文件
确保您有以下文件结构:
```
AutoHotkey/
├── POE2_IbInputSimulator_Macro.ahk
├── IbInputSimulator使用说明.md
└── IbInputSimulator.AHK2/
    ├── IbInputSimulator.ahk
    ├── IbInputSimulator.dll
    └── test/ (测试文件)
```

### 2. 选择并安装驱动

#### 🟢 推荐: Logitech G HUB (新版)
- **优点**: 无需Logitech硬件，兼容性好
- **安装**: 下载并安装 [Logitech G HUB](https://www.logitechg.com/innovation/g-hub.html)
- **注意**: 新版本可能移除了鼠标驱动，但键盘驱动仍可用

#### 🟡 备选: Logitech 传统版
- **适用**: 旧版LGS或G HUB
- **安装**: 安装 Logitech Gaming Software v9.02.65

#### 🟡 备选: Razer Synapse 3
- **要求**: 需要Razer硬件
- **安装**: 下载并安装 [Razer Synapse 3](https://www.razer.com/synapse-3)

#### 🔴 谨慎: DD虚拟设备
- **风险**: 可能导致蓝屏，难以完全卸载
- **仅限**: 高级用户，有完整备份

#### 🟢 安全: 标准输入
- **说明**: 使用Windows标准输入API
- **限制**: 可能被POE2阻止

## 🎮 使用方法

### 1. 启动脚本
1. **以管理员身份运行** `POE2_IbInputSimulator_Macro.ahk`
2. 界面将显示驱动选择和状态信息

### 2. 初始化驱动
1. 在下拉菜单中选择合适的驱动程序
2. 点击 **"初始化"** 按钮
3. 等待驱动初始化完成 (绿色状态)

### 3. 测试驱动
1. 点击 **"测试输入 (F2)"** 按钮
2. 切换到记事本等程序
3. 观察是否有文字输出

### 4. 启动宏功能
1. 启动POE2游戏
2. 点击 **"启动宏 (F1)"** 按钮
3. 宏将开始自动循环发送输入

## ⌨️ 快捷键

- **F1**: 启动/停止宏
- **F2**: 测试驱动输入
- **F3**: 重新初始化驱动
- **F12**: 退出程序

## 🔧 自定义配置

### 修改输入内容
在 `MacroMainLoop()` 函数中修改发送的按键:

```autohotkey
; 当前发送空格键
IbSend("{Space}")

; 可以改为其他按键
IbSend("{Enter}")     ; 回车键
IbSend("1")           ; 数字1
IbSend("{F1}")        ; F1键
```

### 修改循环间隔
在 `ToggleMacroState()` 函数中修改定时器间隔:

```autohotkey
; 当前880ms循环 (80ms按下 + 800ms等待)
SetTimer(MacroMainLoop, 880)

; 可以改为其他间隔
SetTimer(MacroMainLoop, 1000)  ; 1秒循环
SetTimer(MacroMainLoop, 500)   ; 0.5秒循环
```

## 🐛 故障排除

### 驱动初始化失败
**可能原因**:
- 未以管理员身份运行
- 对应驱动软件未安装
- 驱动软件版本不兼容

**解决方案**:
1. 右键脚本 → "以管理员身份运行"
2. 安装对应的驱动软件
3. 尝试其他驱动程序
4. 重启计算机后再试

### 测试输入无效果
**可能原因**:
- 驱动程序未正确初始化
- 目标程序不接受该类型输入
- 权限不足

**解决方案**:
1. 重新初始化驱动程序
2. 尝试不同的驱动程序
3. 确保以管理员身份运行

### POE2无响应
**可能原因**:
- POE2使用了输入保护机制
- 发送的按键类型不正确
- 游戏窗口未激活

**解决方案**:
1. 确保POE2窗口处于激活状态
2. 尝试发送不同类型的按键
3. 检查POE2的按键绑定设置

## 🔍 技术原理

### IbInputSimulator工作原理
1. **驱动级注入**: 直接通过硬件驱动发送输入
2. **绕过限制**: 避开Windows用户模式输入限制
3. **硬件模拟**: 模拟真实硬件设备的输入信号

### 与传统方法的区别
| 方法 | 原理 | 优点 | 缺点 |
|------|------|------|------|
| SendInput | Windows API | 简单易用 | 容易被阻止 |
| IbInputSimulator | 驱动程序 | 难以检测 | 需要驱动支持 |
| 硬件模拟 | 物理设备 | 完全真实 | 成本高，复杂 |

## 📚 进阶使用

### 添加新的驱动支持
在 `DRIVERS` 数组中添加新的驱动选项:

```autohotkey
DRIVERS := [
    ; 现有驱动...
    {name: "新驱动", value: "NewDriver", desc: "新驱动描述"}
]
```

### 实现复杂输入序列
修改 `MacroMainLoop()` 实现复杂的按键序列:

```autohotkey
MacroMainLoop() {
    ; 发送组合键
    IbSend("^c")  ; Ctrl+C
    Sleep(100)
    IbSend("^v")  ; Ctrl+V
    
    ; 发送鼠标操作
    IbClick("Left")
    IbMouseMove(100, 100)
}
```

## ⚠️ 注意事项

1. **管理员权限**: 必须以管理员身份运行
2. **驱动兼容性**: 不同驱动程序兼容性不同
3. **游戏检测**: 某些游戏可能检测并阻止此类输入
4. **系统稳定性**: DD驱动可能影响系统稳定性
5. **合规使用**: 请遵守游戏服务条款

## 🆘 获取帮助

如果遇到问题:
1. 查看本文档的故障排除部分
2. 检查 [IbInputSimulator GitHub](https://github.com/Chaoses-Ib/IbInputSimulator) 的Issues
3. 确保使用最新版本的驱动程序
4. 尝试不同的驱动程序选项

## 🎮 新增：GHUB + reWASD 硬件按键方案

### 专用脚本文件
- **IbInputSimulator_GHUB_硬件按键.ahk** - 专门用于 GHUB 驱动的硬件按键发送器
- **GHUB_reWASD_兼容性测试.ahk** - 兼容性测试工具
- **POE2_GHUB_reWASD_示例.ahk** - 完整的游戏自动化示例
- **IbInputSimulator_reWASD_配置指南.md** - 详细配置指南

### 工作原理
```
AutoHotkey → IbInputSimulator → GHUB驱动 → 虚拟硬件设备 → reWASD拦截 → 控制器信号 → 游戏
```

### 优势
- **硬件级输入**: 通过 Logitech G HUB 驱动发送真正的硬件信号
- **reWASD 兼容**: 可被 reWASD 识别并重映射为控制器按键
- **绕过限制**: 有效绕过游戏的键盘输入限制
- **稳定可靠**: 经过测试验证的兼容性方案

### 快速开始
1. 安装 Logitech G HUB 软件
2. 安装 reWASD 软件
3. 运行 `GHUB_reWASD_兼容性测试.ahk` 验证兼容性
4. 使用 `IbInputSimulator_GHUB_硬件按键.ahk` 发送硬件按键
5. 在 reWASD 中配置按键映射
6. 参考 `POE2_GHUB_reWASD_示例.ahk` 实现游戏自动化

详细配置请参考 `IbInputSimulator_reWASD_配置指南.md`

## 📄 许可证

本项目基于MIT许可证，IbInputSimulator库也使用MIT许可证。