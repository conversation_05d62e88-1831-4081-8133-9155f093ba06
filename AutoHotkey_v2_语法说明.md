# AutoHotkey v2.0 语法说明

## 🔄 从 v1.1 到 v2.0 的主要变化

### 1. 变量声明
```autohotkey
; v1.1 (旧语法)
MyVar := "value"

; v2.0 (新语法)
local MyVar := "value"    ; 局部变量
global MyVar := "value"   ; 全局变量
```

### 2. 函数参数
```autohotkey
; v1.1 (旧语法)
MyFunction(Param1, Param2)

; v2.0 (新语法)
MyFunction(Param1, Param2) {
    ; 参数自动为局部变量
}
```

### 3. Lambda 函数
```autohotkey
; v2.0 正确语法
Button.OnEvent("Click", (*) => MsgBox("单行代码"))

; 多行代码需要用函数
Button.OnEvent("Click", MyClickHandler)
MyClickHandler(*) {
    A_Clipboard := "text"
    MsgBox("多行代码")
}

; 错误语法（不支持）
Button.OnEvent("Click", (*) => {
    A_Clipboard := "text"  ; 这样会报错
    MsgBox("多行代码")
})
```

### 4. 字符串处理
```autohotkey
; v1.1 (旧语法)
Var = %OtherVar%

; v2.0 (新语法)
Var := OtherVar
```

### 5. 数组和对象
```autohotkey
; v1.1 (旧语法)
Array := ["item1", "item2"]
Object := {key1: "value1", key2: "value2"}

; v2.0 (新语法 - 相同，但更严格)
Array := ["item1", "item2"]
Object := {key1: "value1", key2: "value2"}
```

### 6. OnExit 回调函数
```autohotkey
; v1.1 (旧语法)
OnExit("MyExitFunction")
MyExitFunction() {
    ; 清理代码
}

; v2.0 (新语法)
OnExit(MyExitFunction)
MyExitFunction(ExitReason, ExitCode) {
    ; 清理代码
    ; ExitReason: 退出原因
    ; ExitCode: 退出代码
}
```

### 7. GUI 对象方法
```autohotkey
; v1.1 (旧语法)
Gui, Add, Button, , 关闭
Gui, Close

; v2.0 (新语法)
myGui := Gui()
myGui.AddButton(, "关闭")
myGui.Destroy()  ; 关闭并销毁 GUI
myGui.Hide()     ; 隐藏 GUI
myGui.Show()     ; 显示 GUI
```

## 🚨 常见错误和修复

### 错误 1: Lambda 函数中的多行代码
```autohotkey
; ❌ 错误
Button.OnEvent("Click", (*) => {
    A_Clipboard := testResults
    MsgBox("复制成功")
})

; ✅ 正确
Button.OnEvent("Click", CopyHandler)
CopyHandler(*) {
    A_Clipboard := testResults
    MsgBox("复制成功")
}
```

### 错误 2: 未声明的局部变量
```autohotkey
; ❌ 错误
MyFunction() {
    gui := Gui()  ; 错误：未声明局部变量
}

; ✅ 正确
MyFunction() {
    local gui := Gui()  ; 正确：声明局部变量
}
```

### 错误 3: 字符串中的引号
```autohotkey
; ❌ 错误
MsgBox("请选择"确定"按钮")

; ✅ 正确方法1：使用单引号
MsgBox("请选择'确定'按钮")

; ✅ 正确方法2：转义引号
MsgBox("请选择""确定""按钮")
```

### 错误 4: 全局变量访问
```autohotkey
; ❌ 错误
global MyVar := "value"
MyFunction() {
    MyVar := "new value"  ; 错误：未声明全局访问
}

; ✅ 正确
global MyVar := "value"
MyFunction() {
    global MyVar
    MyVar := "new value"  ; 正确：声明了全局访问
}
```

### 错误 5: OnExit 回调函数参数
```autohotkey
; ❌ 错误
OnExit(CleanUp)
CleanUp() {  ; 错误：缺少必需的参数
    ; 清理代码
}

; ✅ 正确
OnExit(CleanUp)
CleanUp(ExitReason, ExitCode) {  ; 正确：包含必需的参数
    ; 清理代码
}
```

### 错误 6: GUI 对象方法名称
```autohotkey
; ❌ 错误
myGui := Gui()
Button.OnEvent("Click", (*) => myGui.Close())  ; 错误：v2.0 中没有 Close() 方法

; ✅ 正确
myGui := Gui()
Button.OnEvent("Click", (*) => myGui.Destroy())  ; 正确：使用 Destroy() 方法
```

## 📋 本项目中修复的问题

### 1. GUI 创建函数
```autohotkey
; 修复前
CreateGUI() {
    gui := Gui()  ; 错误：未声明局部变量
    statusLabel := gui.AddText()  ; 错误：未声明局部变量
}

; 修复后
CreateGUI() {
    local gui := Gui()  ; 正确：声明局部变量
    local statusLabel := gui.AddText()  ; 正确：声明局部变量
}
```

### 2. 事件处理
```autohotkey
; 修复前
keyEdit.OnEvent("Change", (*) => global currentKey := keyEdit.Text)  ; 错误

; 修复后
keyEdit.OnEvent("Change", UpdateCurrentKey)  ; 正确
UpdateCurrentKey(ctrl, *) {
    global currentKey
    currentKey := ctrl.Text
}
```

### 3. 按钮事件
```autohotkey
; 修复前
Button.OnEvent("Click", (*) => {
    A_Clipboard := testResults
    MsgBox("复制成功")
})  ; 错误：多行 lambda

; 修复后
Button.OnEvent("Click", CopyResults)  ; 正确
CopyResults(*) {
    A_Clipboard := testResults
    MsgBox("复制成功")
}
```

## 🎯 最佳实践

### 1. 始终声明变量类型
```autohotkey
MyFunction() {
    local localVar := "value"    ; 局部变量
    global globalVar             ; 全局变量访问
    static staticVar := "value"  ; 静态变量
}
```

### 2. 使用独立函数而非复杂 Lambda
```autohotkey
; 推荐：简单 lambda
Button.OnEvent("Click", (*) => ExitApp())

; 推荐：复杂逻辑用独立函数
Button.OnEvent("Click", HandleComplexClick)
HandleComplexClick(*) {
    ; 复杂逻辑
}
```

### 3. 字符串处理
```autohotkey
; 推荐：使用单引号避免转义
MsgBox("点击'确定'继续")

; 或者：正确转义双引号
MsgBox("点击""确定""继续")
```

### 4. 错误处理
```autohotkey
try {
    ; 可能出错的代码
    result := SomeFunction()
} catch Error as e {
    MsgBox("错误: " . e.Message)
}
```

## 🔧 调试技巧

### 1. 使用 IDE 语法检查
- 使用支持 AutoHotkey v2.0 的编辑器
- 启用语法高亮和错误检查

### 2. 分步测试
```autohotkey
; 添加调试输出
MsgBox("调试: 变量值 = " . MyVar)

; 使用 OutputDebug
OutputDebug("调试信息: " . A_Now)
```

### 3. 常见错误信息
- `Missing "propertyname:" in object literal` → Lambda 语法错误
- `This local variable has not been assigned a value` → 变量声明问题
- `Missing space or operator` → 字符串引号问题

## 📚 参考资源

- [AutoHotkey v2.0 官方文档](https://www.autohotkey.com/docs/v2/)
- [v1.1 到 v2.0 迁移指南](https://www.autohotkey.com/docs/v2/v1-changes.htm)
- [AutoHotkey v2.0 语法参考](https://www.autohotkey.com/docs/v2/Language.htm)

---

**总结**: AutoHotkey v2.0 更加严格和现代化，需要明确的变量声明和正确的语法结构。本项目已经完全适配 v2.0 语法规范。
