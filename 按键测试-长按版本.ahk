#Requires AutoHotkey v2.0
#SingleInstance Force

; ===================================================================
; 按键测试 - 不同发送方式演示
; 功能: 演示单击、长按、自定义时长的按键发送
; ===================================================================

; 尝试包含IbInputSimulator库
try {
    #Include "IbInputSimulator\IbInputSimulator.ahk"
    ibAvailable := true
} catch {
    ibAvailable := false
}

; 全局变量
driverInitialized := false
isRunning := false

; GUI控件
mainWindow := ""
statusLabel := ""
methodLabel := ""

; 创建界面
CreateTestInterface()

; 热键绑定
F1::TestSingleClick()
F2::TestLongPress()
F3::TestCustomDuration()
F4::InitializeDriver()
F5::ToggleAutoSend()
F12::ExitApp()

; ===================================================================
; 创建测试界面
; ===================================================================
CreateTestInterface() {
    global mainWindow, statusLabel, methodLabel
    
    mainWindow := Gui("+Resize", "按键发送方式测试")
    
    ; 标题
    mainWindow.AddText("x20 y20 w400 h30 Center", "⌨️ 按键发送方式测试")
    mainWindow.AddText("x20 y50 w400 h20 Center cBlue", "测试不同的按键发送方式")
    mainWindow.AddText("x20 y75 w400 h2 0x10")
    
    ; 状态显示
    mainWindow.AddText("x20 y90 w80 h25", "驱动状态:")
    if (ibAvailable) {
        statusLabel := mainWindow.AddText("x100 y90 w200 h25 cGreen", "● IbInputSimulator 可用")
    } else {
        statusLabel := mainWindow.AddText("x100 y90 w200 h25 cRed", "● IbInputSimulator 不可用")
    }
    
    mainWindow.AddText("x20 y120 w80 h25", "当前方式:")
    methodLabel := mainWindow.AddText("x100 y120 w200 h25", "● 未选择")
    
    ; 测试按钮
    mainWindow.AddText("x20 y155 w400 h2 0x10")
    mainWindow.AddText("x20 y170 w400 h20 Center", "测试方式 (请先打开记事本)")
    
    btn1 := mainWindow.AddButton("x20 y200 w90 h35", "单击 (F1)")
    btn1.OnEvent("Click", TestSingleClick)
    
    btn2 := mainWindow.AddButton("x120 y200 w90 h35", "长按 (F2)")
    btn2.OnEvent("Click", TestLongPress)
    
    btn3 := mainWindow.AddButton("x220 y200 w90 h35", "自定义 (F3)")
    btn3.OnEvent("Click", TestCustomDuration)
    
    btn4 := mainWindow.AddButton("x320 y200 w90 h35", "初始化 (F4)")
    btn4.OnEvent("Click", InitializeDriver)
    
    btn5 := mainWindow.AddButton("x170 y250 w90 h35", "自动循环 (F5)")
    btn5.OnEvent("Click", ToggleAutoSend)
    
    ; 说明区域
    mainWindow.AddText("x20 y300 w400 h2 0x10")
    mainWindow.AddText("x20 y315 w400 h120 Wrap",
        "🎯 测试说明:`n" .
        "• 单击: 瞬间按下并释放 (适合大多数技能)`n" .
        "• 长按: 按下500ms后释放 (适合持续技能)`n" .
        "• 自定义: 按下80ms后释放 (POE2推荐时长)`n" .
        "• 自动循环: 每880ms发送一次自定义按键`n`n" .
        "⚠️ 请先打开记事本测试，确认效果后再在游戏中使用")
    
    btnExit := mainWindow.AddButton("x320 y450 w90 h30", "退出 (F12)")
    btnExit.OnEvent("Click", (*) => ExitApp())
    
    mainWindow.Show("w440 h500")
}

; ===================================================================
; 初始化驱动
; ===================================================================
InitializeDriver(*) {
    global driverInitialized, statusLabel
    
    if (!ibAvailable) {
        MsgBox("IbInputSimulator库不可用!", "错误", 16)
        return
    }
    
    try {
        IbSendInit("LogitechGHubNew")
        driverInitialized := true
        statusLabel.Text := "● Logitech驱动已初始化"
        statusLabel.Opt("cGreen")
        MsgBox("驱动初始化成功!", "成功", 64)
    } catch Error as e {
        MsgBox("驱动初始化失败: " . e.Message, "错误", 16)
    }
}

; ===================================================================
; 测试单击 (瞬间按下释放)
; ===================================================================
TestSingleClick(*) {
    global methodLabel
    
    methodLabel.Text := "● 单击模式"
    methodLabel.Opt("cBlue")
    
    MsgBox("3秒后发送单击按键'1'", "单击测试", 64)
    Sleep(3000)
    
    if (driverInitialized) {
        IbSend("4")  ; 瞬间按下释放
    } else {
        Send("4")    ; 标准单击
    }
    
    MsgBox("单击发送完成!", "完成", 64)
}

; ===================================================================
; 测试长按 (500ms)
; ===================================================================
TestLongPress(*) {
    global methodLabel
    
    methodLabel.Text := "● 长按模式 (500ms)"
    methodLabel.Opt("cOrange")
    
    MsgBox("3秒后发送长按按键'1' (持续500ms)", "长按测试", 64)
    Sleep(3000)
    
    if (driverInitialized) {
        ; IbInputSimulator长按方式
        IbSend("{1 down}")  ; 按下
        Sleep(500)          ; 持续500ms
        IbSend("{1 up}")    ; 释放
    } else {
        ; 标准AHK长按方式
        Send("{1 down}")
        Sleep(500)
        Send("{1 up}")
    }
    
    MsgBox("长按发送完成!", "完成", 64)
}

; ===================================================================
; 测试自定义时长 (80ms - POE2推荐)
; ===================================================================
TestCustomDuration(*) {
    global methodLabel
    
    methodLabel.Text := "● 自定义模式 (80ms)"
    methodLabel.Opt("cGreen")
    
    MsgBox("3秒后发送自定义按键'1' (持续80ms)", "自定义测试", 64)
    Sleep(3000)
    
    if (driverInitialized) {
        ; IbInputSimulator自定义时长
        IbSend("{1 down}")  ; 按下
        Sleep(80)           ; 持续80ms
        IbSend("{1 up}")    ; 释放
    } else {
        ; 标准AHK自定义时长
        Send("{1 down}")
        Sleep(80)
        Send("{1 up}")
    }
    
    MsgBox("自定义发送完成!", "完成", 64)
}

; ===================================================================
; 切换自动发送
; ===================================================================
ToggleAutoSend(*) {
    global isRunning, methodLabel
    
    if (!isRunning) {
        isRunning := true
        methodLabel.Text := "● 自动循环中 (80ms按键)"
        methodLabel.Opt("cRed")
        SetTimer(AutoSendKey, 880)
        MsgBox("开始自动循环发送 (每880ms一次)", "自动循环", 64)
    } else {
        isRunning := false
        methodLabel.Text := "● 已停止"
        methodLabel.Opt("cGray")
        SetTimer(AutoSendKey, 0)
        MsgBox("停止自动循环", "停止", 64)
    }
}

; ===================================================================
; 自动发送按键 (80ms时长)
; ===================================================================
AutoSendKey() {
    global isRunning, driverInitialized
    
    if (!isRunning) {
        return
    }
    
    try {
        if (driverInitialized) {
            IbSend("{1 down}")
            Sleep(80)
            IbSend("{1 up}")
        } else {
            Send("{1 down}")
            Sleep(80)
            Send("{1 up}")
        }
    } catch Error as e {
        ToggleAutoSend()  ; 出错时停止
        MsgBox("发送失败: " . e.Message, "错误", 16)
    }
} 