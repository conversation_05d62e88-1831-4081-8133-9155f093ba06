#Requires AutoHotkey v2.0
#SingleInstance Force
#WinActivateForce

; 强制导入IbInputSimulator库 - 必须在文件顶部
#Include "IbInputSimulator\IbInputSimulator.ahk"

; 设置高性能参数 - AutoHotkey v2语法
A_MaxHotkeysPerInterval := 99000000
A_HotkeyInterval := 99000000
KeyHistory(0)  ; 禁用按键历史
SetWinDelay(-1)  ; 无窗口延迟
SetControlDelay(-1)  ; 无控件延迟
ProcessSetPriority("High")  ; 设置高优先级

; ===================================================================
; POE2 血量检测器 - 高性能版本 + 自动药水
; 功能: 实时检测血量并自动使用药水
; ===================================================================

; 全局变量声明
global IbInputSimulatorAvailable := false
global InputSimulatorReady := false
global InputSimulatorType := ""

; 检查IbInputSimulator函数是否可用
CheckIbInputSimulator() {
    try {
        ; 检查关键函数是否存在
        if (Type(IbSendInit) == "Func" && Type(IbSend) == "Func" && Type(IbSendDestroy) == "Func") {
            return true
        }
        return false
    } catch Error as e {
        return false
    }
}

; 初始化IbInputSimulator
InitializeIbInputSimulator() {
    global IbInputSimulatorAvailable, InputSimulatorReady, InputSimulatorType
    
    ; 检查函数是否可用
    if (!CheckIbInputSimulator()) {
        MsgBox("❌ IbInputSimulator 库函数不可用！`n`n" .
               "请确保以下文件存在：`n" .
               "• IbInputSimulator\IbInputSimulator.ahk`n" .
               "• IbInputSimulator\IbInputSimulator.dll`n`n" .
               "脚本将退出。", "IbInputSimulator 库错误", 16)
        ExitApp()
    }
    
    IbInputSimulatorAvailable := true
    
    ; 按优先级尝试不同驱动
    driverList := [
        {name: "LogitechGHubNew", display: "Logitech G HUB (新版)"},
    ]
    
    for driver in driverList {
        try {
            IbSendInit(driver.name, 1)
            InputSimulatorReady := true
            InputSimulatorType := driver.name
            TrayTip("输入模拟器", "IbInputSimulator (" . driver.display . ") 初始化成功", 2)
            return true
        } catch Error as e {
            ; 继续尝试下一个驱动
            continue
        }
    }
    
    ; 如果所有驱动都失败
    MsgBox("❌ IbInputSimulator 所有驱动初始化失败！`n`n" .
           "尝试的驱动：`n" .
           "• Logitech G HUB (新版/旧版)`n" .
           "• DD驱动`n" .
           "• Razer驱动`n" .
           "• SendInput API`n" .
           "• 任意可用驱动`n`n" .
           "请检查：`n" .
           "1. 是否以管理员权限运行`n" .
           "2. 是否安装了相应的游戏外设软件`n" .
           "3. IbInputSimulator.dll 是否正确`n`n" .
           "脚本将退出。", "IbInputSimulator 驱动错误", 16)
    ExitApp()
}

; 执行初始化
InitializeIbInputSimulator()

; 精确的血球参数计算 - 根据实际坐标修正
HealthBall := {}
HealthBall.topX := 1780
HealthBall.topY := 864
HealthBall.bottomX := 1780  
HealthBall.bottomY := 1035
HealthBall.centerX := 1780  ; 中心X坐标
HealthBall.centerY := Round((864 + 1035) / 2)  ; 中心Y = 949.5
HealthBall.radius := Round((1035 - 864) / 2)   ; 半径 = 85.5
HealthBall.diameter := 1035 - 864  ; 直径 = 171

; POE2 血量检测器类 - 高性能版本 + 自动药水
class POE2HealthDetector {
    __New() {
        ; 预计算所有检测点和常量
        this.InitOptimizedPoints()
        
        ; 状态追踪
        this.currentHealth := 100
        this.lastHealth := 100
        this.healthHistory := []
        this.maxHistorySize := 5  ; 减少历史记录以提高性能
        
        ; 高性能设置
        this.precisionMode := "ultra"  ; 默认最高精度
        this.updateInterval := 16      ; 60FPS更新频率
        
        ; 显示设置
        this.showOverlay := true
        this.showDebugInfo := false
        this.overlayX := HealthBall.centerX + 200
        this.overlayY := HealthBall.centerY - 100
        
        ; 性能优化缓存
        this.lastDetectionTime := 0
        this.cachedResult := 100
        this.cacheValidTime := 10  ; 缓存有效时间(ms)
        
        ; 预计算颜色判断阈值
        this.redThresholds := {
            highLight: {total: 400, ratio: 0.45, r: 120},
            standard: {r: 100, ratio: 0.5},
            dark: {r: 60, ratio: 0.55, multiplier: 1.5},
            deep: {r: 40, ratio: 0.6, multiplier: 2}
        }
        
        this.bgThresholds := {
            dark: 30,
            gray: {diff: 20, max: 80},
            nonRed: 50
        }
        
        ; 自动药水设置
        this.autoPotion := {
            enabled: false,
            skill4Enabled: true,     ; 技能4自动释放
            skill5Enabled: true,     ; 药水5自动使用
            skill4Threshold: 30,     ; 技能4阈值：血量>30%时使用
            skill5Threshold: 80,     ; 药水5阈值：血量<80%时使用
            skill4Interval: 800,     ; 技能4间隔800ms
            skill5Interval: 500,     ; 药水5间隔500ms
            lastSkill4Time: 0,       ; 上次使用技能4的时间
            lastSkill5Time: 0,       ; 上次使用药水5的时间
            skill4Count: 0,          ; 技能4使用次数
            skill5Count: 0           ; 药水5使用次数
        }
        
        ; 设置输入模拟器状态
        this.inputSimReady := InputSimulatorReady
        this.useIbInputSim := true
        this.inputSimType := InputSimulatorType
    }
    
    InitOptimizedPoints() {
        ; 优化的扫描点 - 减少计算量
        cx := HealthBall.centerX
        r := HealthBall.radius
        
        ; 只保留关键扫描列，提高性能
        this.scanColumns := [-10, 0, 10]  ; 3条扫描线足够
        
        ; 预计算Y坐标范围
        this.topEdge := HealthBall.topY
        this.bottomEdge := HealthBall.bottomY
        this.totalHeight := HealthBall.diameter
        this.heightInv := 1.0 / this.totalHeight  ; 预计算倒数避免除法
    }
    
    ; 超高性能检测 - 优化版本
    DetectHealthFast() {
        ; 性能缓存检查
        currentTime := A_TickCount
        if (currentTime - this.lastDetectionTime < this.cacheValidTime) {
            return this.cachedResult
        }
        
        cx := HealthBall.centerX
        totalBoundary := 0
        validScans := 0
        
        ; 优化的多列扫描
        for xOffset in this.scanColumns {
            currentX := cx + xOffset
            boundary := this.FindRedTopBoundaryFast(currentX)
            
            if (boundary < this.bottomEdge) {
                redHeight := this.bottomEdge - boundary
                totalBoundary += redHeight
                validScans++
            }
        }
        
        result := 0
        if (validScans > 0) {
            avgRedHeight := totalBoundary / validScans
            result := Round(avgRedHeight * this.heightInv * 100, 1)
            result := Max(0, Min(100, result))
        }
        
        ; 更新缓存
        this.lastDetectionTime := currentTime
        this.cachedResult := result
        
        return result
    }
    
    ; 优化的边界检测 - 减少函数调用
    FindRedTopBoundaryFast(x) {
        firstRedY := this.bottomEdge
        
        ; 优化的扫描循环 - 减少函数调用开销
        y := this.bottomEdge
        while (y > this.topEdge) {
            color := PixelGetColor(x, y, "RGB")
            
            ; 内联颜色判断以提高性能
            if (this.IsRedColorFast(color)) {
                firstRedY := y
            } else if (this.IsBackgroundColorFast(color)) {
                break
            }
            
            y -= 2  ; 跳跃式扫描，提高速度
        }
        
        return firstRedY
    }
    
    ; 高性能颜色判断 - 内联优化
    IsRedColorFast(color) {
        r := (color >> 16) & 0xFF
        g := (color >> 8) & 0xFF
        b := color & 0xFF
        
        total := r + g + b
        if (total == 0) {
            return false
        }
        
        redRatio := r / total
        
        ; 使用预计算的阈值，减少重复计算
        thresholds := this.redThresholds
        
        ; 高光区域
        if (total > thresholds.highLight.total && redRatio > thresholds.highLight.ratio && r > thresholds.highLight.r) {
            return true
        }
        
        ; 标准红色区域
        if (r > thresholds.standard.r && redRatio > thresholds.standard.ratio && r > g + b) {
            return true
        }
        
        ; 暗红色区域
        if (r > thresholds.dark.r && redRatio > thresholds.dark.ratio && r > (g + b) * thresholds.dark.multiplier) {
            return true
        }
        
        ; 深红色区域
        if (r > thresholds.deep.r && redRatio > thresholds.deep.ratio && r > (g + b) * thresholds.deep.multiplier) {
            return true
        }
        
        return false
    }
    
    ; 高性能背景色判断
    IsBackgroundColorFast(color) {
        r := (color >> 16) & 0xFF
        g := (color >> 8) & 0xFF
        b := color & 0xFF
        
        thresholds := this.bgThresholds
        
        ; 黑色或深色背景
        if (r < thresholds.dark && g < thresholds.dark && b < thresholds.dark) {
            return true
        }
        
        ; 灰色背景
        if (Abs(r - g) < thresholds.gray.diff && Abs(g - b) < thresholds.gray.diff && Abs(r - b) < thresholds.gray.diff && r < thresholds.gray.max) {
            return true
        }
        
        ; 蓝色或其他非红色
        if (r < thresholds.nonRed && (g > r || b > r)) {
            return true
        }
        
        return false
    }
    
    ; 主检测函数 - 高性能版本
    DetectHealth() {
        return this.DetectHealthFast()
    }
    
    ; 自动药水逻辑
    ProcessAutoPotion() {
        if (!this.autoPotion.enabled || !this.inputSimReady) {
            return
        }
        
        currentTime := A_TickCount
        health := this.currentHealth
        
        ; 技能4逻辑：血量>30%时使用，间隔800ms
        if (this.autoPotion.skill4Enabled && health > this.autoPotion.skill4Threshold) {
            if (currentTime - this.autoPotion.lastSkill4Time >= this.autoPotion.skill4Interval) {
                this.UseSkill4()
                this.autoPotion.lastSkill4Time := currentTime
                this.autoPotion.skill4Count++
            }
        }
        
        ; 药水5逻辑：血量<80%时使用，间隔500ms
        if (this.autoPotion.skill5Enabled && health < this.autoPotion.skill5Threshold) {
            if (currentTime - this.autoPotion.lastSkill5Time >= this.autoPotion.skill5Interval) {
                this.UsePotion5()
                this.autoPotion.lastSkill5Time := currentTime
                this.autoPotion.skill5Count++
            }
        }
    }
    
    ; 使用技能4 - 强制使用IbInputSimulator
    UseSkill4() {
        global InputSimulatorReady
        try {
            if (!InputSimulatorReady || !CheckIbInputSimulator()) {
                throw Error("IbInputSimulator不可用")
            }
            IbSend("4")
            ; 可选：添加调试信息
            if (this.showDebugInfo) {
                TrayTip("自动技能", "使用技能4 - 血量: " . this.currentHealth . "%", 1)
            }
        } catch Error as e {
            ; 如果IbSend失败，显示错误
            if (this.showDebugInfo) {
                TrayTip("按键发送错误", "技能4发送失败: " . e.Message, 2)
            }
        }
    }
    
    ; 使用药水5 - 强制使用IbInputSimulator
    UsePotion5() {
        global InputSimulatorReady
        try {
            if (!InputSimulatorReady || !CheckIbInputSimulator()) {
                throw Error("IbInputSimulator不可用")
            }
            IbSend("5")
            ; 可选：添加调试信息
            if (this.showDebugInfo) {
                TrayTip("自动药水", "使用药水5 - 血量: " . this.currentHealth . "%", 1)
            }
        } catch Error as e {
            ; 如果IbSend失败，显示错误
            if (this.showDebugInfo) {
                TrayTip("按键发送错误", "药水5发送失败: " . e.Message, 2)
            }
        }
    }
    
    ; 切换自动药水功能
    ToggleAutoPotion() {
        this.autoPotion.enabled := !this.autoPotion.enabled
        status := this.autoPotion.enabled ? "启用" : "禁用"
        TrayTip("自动药水", "自动药水功能已" . status, 2)
        return this.autoPotion.enabled
    }
    
    ; 优化的历史更新
    UpdateHistory(health) {
        this.lastHealth := this.currentHealth
        this.currentHealth := health
        
        ; 优化的历史记录管理
        this.healthHistory.Push(health)
        if (this.healthHistory.Length > this.maxHistorySize) {
            this.healthHistory.RemoveAt(1)
        }
    }
    
    ; 快速平滑血量计算
    GetSmoothedHealth() {
        if (this.healthHistory.Length == 0) {
            return this.currentHealth
        }
        
        ; 使用简单平均，避免复杂计算
        total := 0
        for health in this.healthHistory {
            total += health
        }
        return Round(total / this.healthHistory.Length, 1)
    }
    
    ; 简化的趋势判断
    GetHealthTrend() {
        if (this.healthHistory.Length < 2) {
            return "稳定"
        }
        
        recent := this.healthHistory[this.healthHistory.Length]
        previous := this.healthHistory[this.healthHistory.Length - 1]
        
        diff := recent - previous
        if (diff > 3) {
            return "上升"
        } else if (diff < -3) {
            return "下降"
        } else {
            return "稳定"
        }
    }
    
    ; 快速状态判断
    GetHealthStatus() {
        health := this.currentHealth
        
        if (health >= 90) {
            return "优秀"
        } else if (health >= 75) {
            return "良好"
        } else if (health >= 50) {
            return "一般"
        } else if (health >= 25) {
            return "较低"
        } else if (health >= 10) {
            return "危险"
        } else {
            return "极危"
        }
    }
    
    ; 快速颜色判断
    GetHealthColor() {
        health := this.currentHealth
        
        if (health >= 80) {
            return "Green"
        } else if (health >= 60) {
            return "Yellow"
        } else if (health >= 40) {
            return "Orange"
        } else if (health >= 20) {
            return "Red"
        } else {
            return "Maroon"
        }
    }
    
    ; 优化的显示更新 - 减少字符串操作
    UpdateDisplay() {
        if (!this.showOverlay) {
            return
        }
        
        health := this.currentHealth
        
        ; 简化显示信息，减少字符串拼接
        info := "🩸 " . health . "%"
        
        ; 显示自动药水状态
        if (this.autoPotion.enabled) {
            info .= " | 🧪自动"
            if (health > this.autoPotion.skill4Threshold) {
                info .= " ⚔️"  ; 技能4激活
            }
            if (health < this.autoPotion.skill5Threshold) {
                info .= " 💊"  ; 药水5激活
            }
        }
        
        if (this.showDebugInfo) {
            smoothed := this.GetSmoothedHealth()
            trend := this.GetHealthTrend()
            info .= " | " . smoothed . "% | " . trend
            info .= " | 技能4:" . this.autoPotion.skill4Count . " 药水5:" . this.autoPotion.skill5Count
        }
        
        ; 显示工具提示
        ToolTip(info, this.overlayX, this.overlayY, 1)
    }
    
    ; 高性能检测循环
    StartDetection() {
        ; 检测血量
        detectedHealth := this.DetectHealth()
        
        ; 更新历史
        this.UpdateHistory(detectedHealth)
        
        ; 处理自动药水
        this.ProcessAutoPotion()
        
        ; 更新显示
        this.UpdateDisplay()
    }
    
    ; 性能监控
    GetPerformanceInfo() {
        return {
            updateInterval: this.updateInterval,
            cacheValidTime: this.cacheValidTime,
            scanColumns: this.scanColumns.Length,
            historySize: this.healthHistory.Length,
            fps: Round(1000 / this.updateInterval, 1),
            autoPotionEnabled: this.autoPotion.enabled,
            skill4Count: this.autoPotion.skill4Count,
            skill5Count: this.autoPotion.skill5Count,
            inputMethod: "IbInputSimulator (" . this.inputSimType . ")"
        }
    }
    
    ; 获取自动药水状态
    GetAutoPotionStatus() {
        global InputSimulatorReady, InputSimulatorType
        return {
            enabled: this.autoPotion.enabled,
            skill4Enabled: this.autoPotion.skill4Enabled,
            skill5Enabled: this.autoPotion.skill5Enabled,
            skill4Threshold: this.autoPotion.skill4Threshold,
            skill5Threshold: this.autoPotion.skill5Threshold,
            skill4Interval: this.autoPotion.skill4Interval,
            skill5Interval: this.autoPotion.skill5Interval,
            skill4Count: this.autoPotion.skill4Count,
            skill5Count: this.autoPotion.skill5Count,
            inputSimReady: InputSimulatorReady,
            inputMethod: "IbInputSimulator (" . InputSimulatorType . ")"
        }
    }
}

; 创建检测器实例
detector := POE2HealthDetector()

; 全局GUI变量
global mainGui := ""

; 创建增强的控制界面 - 高性能版本 + 自动药水
CreateControlInterface() {
    global mainGui
    
    mainGui := Gui("+Resize +AlwaysOnTop", "POE2 血量检测器 - 高性能版 + 自动药水")
    
    ; 简化的标题
    mainGui.AddText("x10 y10 w320 h25 Center", "🩸 POE2 血量检测器 - 高性能版 + 自动药水")
    
    ; 核心控制按钮
    btnStart := mainGui.AddButton("x10 y40 w60 h25", "开始")
    btnStart.OnEvent("Click", StartDetection)
    
    btnStop := mainGui.AddButton("x80 y40 w60 h25", "停止")
    btnStop.OnEvent("Click", StopDetection)
    
    btnTest := mainGui.AddButton("x150 y40 w60 h25", "测试")
    btnTest.OnEvent("Click", SingleDetection)
    
    btnPerf := mainGui.AddButton("x220 y40 w70 h25", "性能信息")
    btnPerf.OnEvent("Click", ShowPerformance)
    
    ; 自动药水控制
    btnAutoPotion := mainGui.AddButton("x10 y75 w80 h25", "自动药水")
    btnAutoPotion.OnEvent("Click", ToggleAutoPotionGUI)
    
    btnPotionStatus := mainGui.AddButton("x100 y75 w80 h25", "药水状态")
    btnPotionStatus.OnEvent("Click", ShowPotionStatus)
    
    btnSettings := mainGui.AddButton("x190 y75 w80 h25", "药水设置")
    btnSettings.OnEvent("Click", ShowPotionSettings)
    
    ; 快速设置
    showOverlayCB := mainGui.AddCheckbox("x10 y110 w80 Checked", "显示覆盖层")
    showOverlayCB.OnEvent("Click", ToggleOverlay)
    
    showDebugCB := mainGui.AddCheckbox("x100 y110 w80", "调试信息")
    showDebugCB.OnEvent("Click", ToggleDebug)
    
    ; 自动药水状态指示
    global autoPotionStatus := mainGui.AddText("x190 y110 w100 h25 Center", "🧪 未启用")
    
    ; 性能状态显示
    mainGui.AddText("x10 y140 w320 h80 VScroll ReadOnly", 
        "⚡ 高性能模式 + IbInputSimulator 已就绪`n" .
        "• F1: 开始/停止检测 (60FPS)`n" .
        "• F2: 单次检测`n" .
        "• F3: 切换自动药水`n" .
        "• F5: 性能状态`n" .
        "• F7: 药水设置`n" .
        "• ESC: 退出`n" .
        "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n" .
        "⚠️ 强制使用IbInputSimulator | 血量>30%使用技能4 | 血量<80%使用药水5")
    
    mainGui.Show("w340 h230")
    return mainGui
}

; 优化的事件处理函数
StartDetection(*) {
    global detector
    SetTimer(DetectionLoop, detector.updateInterval)
    TrayTip("高性能检测器", "开始检测 - " . detector.GetPerformanceInfo().fps . "FPS", 1)
    UpdateAutoPotionStatusDisplay()
}

StopDetection(*) {
    SetTimer(DetectionLoop, 0)
    ToolTip("", , , 1)
    TrayTip("高性能检测器", "停止检测", 1)
}

SingleDetection(*) {
    global detector
    detector.StartDetection()
    TrayTip("血量检测", "当前血量: " . detector.currentHealth . "%", 1)
}

ShowPerformance(*) {
    global detector
    perfInfo := detector.GetPerformanceInfo()
    
    MsgBox("⚡ 性能信息`n" .
           "━━━━━━━━━━━━━━━━`n" .
           "更新频率: " . perfInfo.fps . " FPS`n" .
           "更新间隔: " . perfInfo.updateInterval . " ms`n" .
           "扫描列数: " . perfInfo.scanColumns . "`n" .
           "历史缓存: " . perfInfo.historySize . " 条`n" .
           "缓存时间: " . perfInfo.cacheValidTime . " ms`n" .
           "━━━━━━━━━━━━━━━━`n" .
           "自动药水: " . (perfInfo.autoPotionEnabled ? "启用" : "禁用") . "`n" .
           "技能4使用: " . perfInfo.skill4Count . " 次`n" .
           "药水5使用: " . perfInfo.skill5Count . " 次`n" .
           "输入方式: " . perfInfo.inputMethod . "`n" .
           "━━━━━━━━━━━━━━━━`n" .
           "优化特性: 高优先级进程、缓存机制、内联函数", 
           "性能监控", 64)
}

ToggleAutoPotionGUI(*) {
    global detector
    detector.ToggleAutoPotion()
    UpdateAutoPotionStatusDisplay()
}

ShowPotionStatus(*) {
    global detector
    status := detector.GetAutoPotionStatus()
    health := detector.currentHealth
    
    MsgBox("🧪 自动药水状态`n" .
           "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n" .
           "当前血量: " . health . "%`n" .
           "自动药水: " . (status.enabled ? "✅ 启用" : "❌ 禁用") . "`n" .
           "输入模拟器: " . (status.inputSimReady ? "✅ 就绪" : "❌ 未就绪") . "`n" .
           "输入方式: " . status.inputMethod . "`n" .
           "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n" .
           "技能4设置:`n" .
           "• 启用: " . (status.skill4Enabled ? "是" : "否") . "`n" .
           "• 阈值: 血量 > " . status.skill4Threshold . "%`n" .
           "• 间隔: " . status.skill4Interval . "ms`n" .
           "• 使用次数: " . status.skill4Count . " 次`n" .
           "• 状态: " . (health > status.skill4Threshold ? "🟢 激活" : "🔴 待机") . "`n" .
           "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n" .
           "药水5设置:`n" .
           "• 启用: " . (status.skill5Enabled ? "是" : "否") . "`n" .
           "• 阈值: 血量 < " . status.skill5Threshold . "%`n" .
           "• 间隔: " . status.skill5Interval . "ms`n" .
           "• 使用次数: " . status.skill5Count . " 次`n" .
           "• 状态: " . (health < status.skill5Threshold ? "🟢 激活" : "🔴 待机") . "`n", 
           "自动药水状态", 64)
}

ShowPotionSettings(*) {
    global detector
    status := detector.GetAutoPotionStatus()
    
    MsgBox("⚙️ 自动药水设置`n" .
           "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n" .
           "🔧 当前设置:`n" .
           "• 技能4阈值: 血量 > " . status.skill4Threshold . "% (间隔" . status.skill4Interval . "ms)`n" .
           "• 药水5阈值: 血量 < " . status.skill5Threshold . "% (间隔" . status.skill5Interval . "ms)`n" .
           "• 输入方式: " . status.inputMethod . "`n" .
           "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n" .
           "📋 使用说明:`n" .
           "• 技能4: 血量高于30%时自动释放，间隔800ms`n" .
           "• 药水5: 血量低于80%时自动使用，间隔500ms`n" .
           "• 血量低于30%时停止使用技能4`n" .
           "• 优先使用IbInputSimulator，备用标准按键发送`n" .
           "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n" .
           "🎮 控制方式:`n" .
           "• F3键: 快速切换自动药水功能`n" .
           "• 界面按钮: 详细控制和状态查看`n" .
           "• 实时显示: 覆盖层显示当前状态", 
           "自动药水设置", 64)
}

UpdateAutoPotionStatusDisplay() {
    global detector, autoPotionStatus
    if (detector.autoPotion.enabled) {
        autoPotionStatus.Text := "🧪 已启用"
    } else {
        autoPotionStatus.Text := "🧪 未启用"
    }
}

ToggleOverlay(ctrl, *) {
    global detector
    detector.showOverlay := ctrl.Value
    if (!detector.showOverlay) {
        ToolTip("", , , 1)
    }
}

ToggleDebug(ctrl, *) {
    global detector
    detector.showDebugInfo := ctrl.Value
}

; 高性能检测循环
DetectionLoop() {
    global detector
    detector.StartDetection()
}

; 优化的热键设置
F1:: {
    global detector
    static isPaused := false
    if (isPaused) {
        SetTimer(DetectionLoop, detector.updateInterval)
        TrayTip("高性能检测器", "恢复检测 - " . detector.GetPerformanceInfo().fps . "FPS", 1)
        isPaused := false
    } else {
        SetTimer(DetectionLoop, 0)
        TrayTip("高性能检测器", "暂停检测", 1)
        isPaused := true
    }
    UpdateAutoPotionStatusDisplay()
}

F2::SingleDetection()

; F3切换自动药水
F3:: {
    global detector
    detector.ToggleAutoPotion()
    UpdateAutoPotionStatusDisplay()
}

F5:: {
    global detector
    ; 显示性能状态
    perfInfo := detector.GetPerformanceInfo()
    health := detector.currentHealth
    smoothed := detector.GetSmoothedHealth()
    trend := detector.GetHealthTrend()
    status := detector.GetHealthStatus()
    
    MsgBox("⚡ 高性能血量检测器状态`n" .
           "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n" .
           "当前血量: " . health . "%`n" .
           "平滑血量: " . smoothed . "%`n" .
           "血量状态: " . status . "`n" .
           "变化趋势: " . trend . "`n" .
           "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n" .
           "自动药水: " . (perfInfo.autoPotionEnabled ? "✅ 启用" : "❌ 禁用") . "`n" .
           "技能4使用: " . perfInfo.skill4Count . " 次`n" .
           "药水5使用: " . perfInfo.skill5Count . " 次`n" .
           "输入方式: " . perfInfo.inputMethod . "`n" .
           "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n" .
           "性能信息:`n" .
           "• 检测频率: " . perfInfo.fps . " FPS`n" .
           "• 扫描优化: " . perfInfo.scanColumns . " 列扫描`n" .
           "• 缓存机制: " . perfInfo.cacheValidTime . "ms 有效期`n" .
           "• 历史记录: " . perfInfo.historySize . " 条`n" .
           "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", 
           "高性能状态监控", 64)
}

; 快速调试热键 - 简化版本
F6:: {
    global HealthBall, detector
    
    ; 快速边界检测
    cx := HealthBall.centerX
    boundary := detector.FindRedTopBoundaryFast(cx)
    currentHealth := detector.DetectHealth()
    perfInfo := detector.GetPerformanceInfo()
    potionStatus := detector.GetAutoPotionStatus()
    
    ; 简化的调试信息
    debugInfo := "🔍 快速调试信息`n"
    debugInfo .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n"
    debugInfo .= "检测结果: " . currentHealth . "%`n"
    debugInfo .= "上边界: Y=" . boundary . "`n"
    debugInfo .= "红色高度: " . (HealthBall.bottomY - boundary) . " 像素`n"
    debugInfo .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n"
    debugInfo .= "自动药水状态:`n"
    debugInfo .= "• 功能: " . (potionStatus.enabled ? "启用" : "禁用") . "`n"
    debugInfo .= "• 技能4: " . (currentHealth > potionStatus.skill4Threshold ? "🟢激活" : "🔴待机") . " (>" . potionStatus.skill4Threshold . "%)`n"
    debugInfo .= "• 药水5: " . (currentHealth < potionStatus.skill5Threshold ? "🟢激活" : "🔴待机") . " (<" . potionStatus.skill5Threshold . "%)`n"
    debugInfo .= "• 输入方式: " . potionStatus.inputMethod . "`n"
    debugInfo .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n"
    debugInfo .= "性能模式: 高性能优化`n"
    debugInfo .= "检测频率: " . perfInfo.fps . " FPS`n"
    debugInfo .= "扫描方式: 跳跃式扫描 (步长2)`n"
    debugInfo .= "缓存状态: " . (A_TickCount - detector.lastDetectionTime < detector.cacheValidTime ? "有效" : "过期") . "`n"
    debugInfo .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    MsgBox(debugInfo, "高性能调试", 0)
}

; F7药水设置
F7::ShowPotionSettings()

Esc:: {
    SetTimer(DetectionLoop, 0)
    ToolTip("", , , 1)
    
    ; 强制清理IbInputSimulator资源
    global InputSimulatorReady
    if (InputSimulatorReady && CheckIbInputSimulator()) {
        try {
            IbSendDestroy()
            TrayTip("资源清理", "IbInputSimulator 资源已清理", 1)
        } catch Error as e {
            TrayTip("清理警告", "IbInputSimulator 清理失败: " . e.Message, 2)
        }
    }
    
    ExitApp()
}

; 创建控制界面
CreateControlInterface()

; 启动提示
TrayTip("POE2 高性能血量检测器", "⚡ 高性能模式 + IbInputSimulator 已就绪`n60FPS检测频率`n⚠️ 强制使用IbInputSimulator驱动`n按F1开始检测，F3切换自动药水", 4) 