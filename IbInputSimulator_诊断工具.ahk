#Requires AutoHotkey v2.0
#SingleInstance Force

; ===================================================================
; IbInputSimulator 诊断工具
; 用于诊断IbInputSimulator初始化失败的具体原因
; ===================================================================

; 诊断结果存储
global DiagnosticResults := {
    ahkVersion: "",
    ahkBitness: "",
    workingDir: "",
    scriptDir: "",
    dllExists: false,
    ahkExists: false,
    dllLoadable: false,
    moduleHandle: 0,
    adminRights: false,
    driverResults: Map(),
    recommendations: []
}

; 主诊断函数
RunDiagnostics() {
    results := DiagnosticResults
    
    ; 1. 检查AutoHotkey版本和位数
    results.ahkVersion := A_AhkVersion
    results.ahkBitness := A_PtrSize == 8 ? "64-bit" : "32-bit"
    
    ; 2. 检查工作目录
    results.workingDir := A_WorkingDir
    results.scriptDir := A_ScriptDir
    
    ; 3. 检查文件存在性
    results.dllExists := FileExist(A_ScriptDir . "\IbInputSimulator\IbInputSimulator.dll") ? true : false
    results.ahkExists := FileExist(A_ScriptDir . "\IbInputSimulator\IbInputSimulator.ahk") ? true : false
    
    ; 4. 检查管理员权限
    results.adminRights := A_IsAdmin
    
    ; 5. 尝试加载DLL
    TestDllLoading()
    
    ; 6. 尝试导入库
    TestLibraryImport()
    
    ; 7. 测试各种驱动
    TestDrivers()
    
    ; 8. 生成建议
    GenerateRecommendations()
    
    ; 9. 显示结果
    ShowDiagnosticResults()
}

; 测试DLL加载
TestDllLoading() {
    global DiagnosticResults
    
    try {
        ; 尝试手动加载DLL
        hModule := DllCall("LoadLibrary", "Str", A_ScriptDir . "\IbInputSimulator\IbInputSimulator.dll", "Ptr")
        if (hModule != 0) {
            DiagnosticResults.dllLoadable := true
            DiagnosticResults.moduleHandle := hModule
            DllCall("FreeLibrary", "Ptr", hModule)
        }
    } catch Error as e {
        DiagnosticResults.dllLoadable := false
        DiagnosticResults.dllError := e.Message
    }
}

; 测试库导入
TestLibraryImport() {
    global DiagnosticResults
    
    try {
        ; 尝试导入库
        #Include "IbInputSimulator\IbInputSimulator.ahk"
        DiagnosticResults.libraryImported := true
        
        ; 检查关键函数 - 使用Map对象
        DiagnosticResults.functionsAvailable := Map()
        DiagnosticResults.functionsAvailable["IbSendInit"] := IsSet(IbSendInit)
        DiagnosticResults.functionsAvailable["IbSend"] := IsSet(IbSend)
        DiagnosticResults.functionsAvailable["IbSendDestroy"] := IsSet(IbSendDestroy)
        DiagnosticResults.functionsAvailable["IbSendMode"] := IsSet(IbSendMode)
    } catch Error as e {
        DiagnosticResults.libraryImported := false
        DiagnosticResults.importError := e.Message
    }
}

; 测试各种驱动
TestDrivers() {
    global DiagnosticResults
    
    if (!DiagnosticResults.libraryImported) {
        return
    }
    
    ; 确保关键函数存在
    if (!IsSet(IbSendInit) || !IsSet(IbSendDestroy)) {
        DiagnosticResults.driverTestSkipped := true
        return
    }
    
    drivers := [
        {name: "AnyDriver", display: "任意驱动"},
        {name: "SendInput", display: "SendInput API"},
        {name: "LogitechGHubNew", display: "Logitech G HUB (新版)"},
        {name: "Logitech", display: "Logitech G HUB (旧版)"},
        {name: "DD", display: "DD驱动"},
        {name: "Razer", display: "Razer驱动"}
    ]
    
    for driver in drivers {
        try {
            IbSendInit(driver.name, 0)  ; mode=0 避免改变SendMode
            DiagnosticResults.driverResults[driver.name] := {
                success: true,
                error: ""
            }
            ; 立即清理
            try {
                IbSendDestroy()
            } catch {
                ; 忽略清理错误
            }
        } catch Error as e {
            DiagnosticResults.driverResults[driver.name] := {
                success: false,
                error: e.Message
            }
        }
    }
}

; 生成建议
GenerateRecommendations() {
    global DiagnosticResults
    results := DiagnosticResults
    
    ; 检查AutoHotkey版本
    if (results.ahkBitness != "64-bit") {
        results.recommendations.Push("❌ 必须使用 AutoHotkey 64位版本")
    }
    
    ; 检查文件存在性
    if (!results.dllExists) {
        results.recommendations.Push("❌ IbInputSimulator.dll 文件不存在")
    }
    
    if (!results.ahkExists) {
        results.recommendations.Push("❌ IbInputSimulator.ahk 文件不存在")
    }
    
    ; 检查DLL加载
    if (!results.dllLoadable) {
        results.recommendations.Push("❌ DLL无法加载，可能需要Visual C++运行库")
    }
    
    ; 检查管理员权限
    if (!results.adminRights) {
        results.recommendations.Push("⚠️ 建议以管理员权限运行")
    }
    
    ; 检查库导入
    if (!results.libraryImported) {
        results.recommendations.Push("❌ 库导入失败")
    }
    
    ; 检查驱动结果 - 安全遍历
    successCount := 0
    if (results.HasOwnProp("driverResults") && results.driverResults.Count > 0) {
        for driverName, result in results.driverResults {
            if (result.success) {
                successCount++
            }
        }
    }
    
    if (successCount == 0) {
        results.recommendations.Push("❌ 所有驱动初始化失败")
        results.recommendations.Push("💡 尝试安装 Logitech G HUB 或其他游戏外设软件")
    } else {
        results.recommendations.Push("✅ 找到 " . successCount . " 个可用驱动")
    }
}

; 显示诊断结果
ShowDiagnosticResults() {
    global DiagnosticResults
    results := DiagnosticResults
    
    ; 构建结果文本
    resultText := "🔍 IbInputSimulator 诊断报告`n"
    resultText .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`n"
    
    ; 基本信息
    resultText .= "📋 基本信息:`n"
    resultText .= "• AutoHotkey版本: " . results.ahkVersion . " (" . results.ahkBitness . ")`n"
    resultText .= "• 管理员权限: " . (results.adminRights ? "✅ 是" : "❌ 否") . "`n"
    resultText .= "• 工作目录: " . results.workingDir . "`n"
    resultText .= "• 脚本目录: " . results.scriptDir . "`n"
    resultText .= "`n"
    
    ; 文件检查
    resultText .= "📁 文件检查:`n"
    resultText .= "• IbInputSimulator.dll: " . (results.dllExists ? "✅ 存在" : "❌ 不存在") . "`n"
    resultText .= "• IbInputSimulator.ahk: " . (results.ahkExists ? "✅ 存在" : "❌ 不存在") . "`n"
    resultText .= "• DLL可加载: " . (results.dllLoadable ? "✅ 是" : "❌ 否") . "`n"
    if (results.HasOwnProp("dllError")) {
        resultText .= "• DLL错误: " . results.dllError . "`n"
    }
    resultText .= "`n"
    
    ; 库导入检查
    resultText .= "📚 库导入检查:`n"
    resultText .= "• 库导入: " . (results.libraryImported ? "✅ 成功" : "❌ 失败") . "`n"
    if (results.HasOwnProp("importError")) {
        resultText .= "• 导入错误: " . results.importError . "`n"
    }
    
    if (results.HasOwnProp("functionsAvailable") && results.functionsAvailable.Count > 0) {
        resultText .= "• 函数可用性:`n"
        ; 按固定顺序显示函数
        functionNames := ["IbSendInit", "IbSend", "IbSendDestroy", "IbSendMode"]
        for funcName in functionNames {
            if (results.functionsAvailable.Has(funcName)) {
                available := results.functionsAvailable[funcName]
                resultText .= "  - " . funcName . ": " . (available ? "✅" : "❌") . "`n"
            }
        }
    }
    resultText .= "`n"
    
    ; 驱动测试结果
    if (results.HasOwnProp("driverTestSkipped") && results.driverTestSkipped) {
        resultText .= "🎮 驱动测试结果:`n"
        resultText .= "• ⚠️ 驱动测试被跳过 (关键函数不可用)`n"
        resultText .= "`n"
    } else if (results.HasOwnProp("driverResults") && results.driverResults.Count > 0) {
        resultText .= "🎮 驱动测试结果:`n"
        drivers := [
            {name: "AnyDriver", display: "任意驱动"},
            {name: "SendInput", display: "SendInput API"},
            {name: "LogitechGHubNew", display: "Logitech G HUB (新版)"},
            {name: "Logitech", display: "Logitech G HUB (旧版)"},
            {name: "DD", display: "DD驱动"},
            {name: "Razer", display: "Razer驱动"}
        ]
        
        for driver in drivers {
            if (results.driverResults.Has(driver.name)) {
                result := results.driverResults[driver.name]
                status := result.success ? "✅ 成功" : "❌ 失败"
                resultText .= "• " . driver.display . ": " . status
                if (!result.success && result.error != "") {
                    resultText .= " (" . result.error . ")"
                }
                resultText .= "`n"
            }
        }
        resultText .= "`n"
    }
    
    ; 建议
    if (results.recommendations.Length > 0) {
        resultText .= "💡 建议和解决方案:`n"
        for recommendation in results.recommendations {
            resultText .= "• " . recommendation . "`n"
        }
        resultText .= "`n"
    }
    
    ; 常见解决方案
    resultText .= "🔧 常见解决方案:`n"
    resultText .= "1. 确保使用 AutoHotkey v2.0 64位版本`n"
    resultText .= "2. 以管理员权限运行脚本`n"
    resultText .= "3. 安装 Visual C++ Redistributable (x64)`n"
    resultText .= "4. 安装 Logitech G HUB 或其他游戏外设软件`n"
    resultText .= "5. 检查杀毒软件是否阻止了DLL加载`n"
    resultText .= "6. 确保文件路径中没有中文字符`n"
    resultText .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    ; 显示结果
    MsgBox(resultText, "IbInputSimulator 诊断报告", 0)
    
    ; 同时输出到文件
    try {
        FileAppend(resultText, "IbInputSimulator_诊断报告.txt")
        TrayTip("诊断完成", "报告已保存到 IbInputSimulator_诊断报告.txt", 3)
    } catch {
        TrayTip("诊断完成", "诊断报告已显示", 3)
    }
}

; 创建简单的GUI
CreateDiagnosticGUI() {
    ; 创建GUI对象
    diagGui := Gui("+AlwaysOnTop", "IbInputSimulator 诊断工具")
    
    diagGui.AddText("x10 y10 w300 h30 Center", "🔍 IbInputSimulator 诊断工具")
    diagGui.AddText("x10 y50 w300 h40", "此工具将帮助诊断 IbInputSimulator 初始化失败的原因")
    
    btnDiagnose := diagGui.AddButton("x10 y100 w100 h30", "开始诊断")
    btnDiagnose.OnEvent("Click", (*) => RunDiagnostics())
    
    btnExit := diagGui.AddButton("x120 y100 w100 h30", "退出")
    btnExit.OnEvent("Click", (*) => ExitApp())
    
    diagGui.AddText("x10 y140 w300 h60", 
        "诊断内容包括：`n" .
        "• AutoHotkey版本检查`n" .
        "• 文件存在性检查`n" .
        "• DLL加载测试`n" .
        "• 各种驱动兼容性测试")
    
    diagGui.Show("w320 h210")
    return diagGui
}

; 启动诊断工具
CreateDiagnosticGUI()

; 热键
F1::RunDiagnostics()
Esc::ExitApp() 