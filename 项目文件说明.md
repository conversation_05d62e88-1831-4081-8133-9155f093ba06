# 项目文件结构说明

## 📁 文件组织

```
AutoHotkey/
├── 📚 核心库文件
│   └── IbInputSimulator/
│       ├── IbInputSimulator.ahk          # 主库文件
│       ├── IbInputSimulator.dll          # 核心DLL
│       └── test/                         # 官方测试文件
│
├── 🎮 主要脚本文件
│   ├── IbInputSimulator_GHUB_硬件按键.ahk    # 主要工具：GHUB硬件按键发送器
│   ├── GHUB_reWASD_兼容性测试.ahk           # 测试工具：系统兼容性验证
│   └── POE2_GHUB_reWASD_示例.ahk            # 示例脚本：完整游戏自动化
│
└── 📖 文档文件
    ├── README_GHUB_reWASD_解决方案.md       # 📋 项目总览和快速开始
    ├── IbInputSimulator_reWASD_配置指南.md  # 🔧 详细配置步骤
    ├── IbInputSimulator使用说明.md          # 📚 库使用说明
    ├── IbInputSimulator与reWASD兼容性报告.md # 📊 兼容性测试报告
    ├── AutoHotkey_v2_语法说明.md            # 🔧 AHK v2.0 语法指南
    └── 项目文件说明.md                      # 📁 本文档
```

## 🚀 使用顺序

### 1. 首次使用
```bash
# 第一步：阅读项目总览
README_GHUB_reWASD_解决方案.md

# 第二步：运行兼容性测试
GHUB_reWASD_兼容性测试.ahk

# 第三步：按照配置指南设置
IbInputSimulator_reWASD_配置指南.md
```

### 2. 日常使用
```bash
# 主要工具：发送硬件按键
IbInputSimulator_GHUB_硬件按键.ahk

# 或者：使用完整示例
POE2_GHUB_reWASD_示例.ahk
```

## 📋 文件功能详解

### 🎯 核心脚本

#### IbInputSimulator_GHUB_硬件按键.ahk
- **用途**: 专门的 GHUB 硬件按键发送器
- **特点**: 
  - 图形化界面操作
  - 可配置按键和时间参数
  - 实时状态监控
  - 专门针对 reWASD 优化

#### GHUB_reWASD_兼容性测试.ahk
- **用途**: 系统兼容性全面测试
- **功能**:
  - 检查管理员权限
  - 验证 IbInputSimulator 库
  - 测试 GHUB 驱动初始化
  - 验证硬件按键发送
  - 提供 reWASD 配置指导

#### POE2_GHUB_reWASD_示例.ahk
- **用途**: 完整的游戏自动化示例
- **功能**:
  - 技能循环自动化
  - 药水自动使用
  - 手动技能释放
  - 实际应用演示

### 📖 文档文件

#### README_GHUB_reWASD_解决方案.md
- **内容**: 项目总览、技术架构、快速开始
- **适合**: 初次了解项目的用户

#### IbInputSimulator_reWASD_配置指南.md
- **内容**: 详细的安装和配置步骤
- **适合**: 需要详细配置指导的用户

#### IbInputSimulator使用说明.md
- **内容**: IbInputSimulator 库的使用说明
- **适合**: 需要了解库功能的开发者

#### IbInputSimulator与reWASD兼容性报告.md
- **内容**: 兼容性测试结果和技术分析
- **适合**: 需要了解技术细节的用户

#### AutoHotkey_v2_语法说明.md
- **内容**: AutoHotkey v2.0 语法规范和常见错误修复
- **适合**: 开发者和需要修改脚本的用户

## 🔧 核心库文件

### IbInputSimulator/
这是 IbInputSimulator 库的完整文件夹，包含：
- **IbInputSimulator.ahk**: 主库文件，提供所有API函数
- **IbInputSimulator.dll**: 核心DLL，实现底层驱动调用
- **test/**: 官方测试文件，展示不同驱动的使用方法

## ⚡ 快速参考

### 常用快捷键
- **F1**: 启动/停止宏
- **F2**: 测试按键输入
- **F3**: 重新初始化驱动
- **F9**: 显示状态信息
- **F12**: 退出程序

### 重要配置
```autohotkey
# GHUB 驱动初始化
IbSendInit("LogitechGHubNew", 1)

# 发送硬件按键
IbSend("{1 down}")
Sleep(80)
IbSend("{1 up}")
```

### reWASD 映射示例
```
键盘 "1" → 控制器 "RT" (右扳机)
键盘 "2" → 控制器 "RB" (右肩键)
键盘 "3" → 控制器 "X" 按钮
```

## 🗑️ 已清理的文件

以下文件已被删除（功能重复或过时）：
- ❌ IbInputSimulator_快速测试.ahk
- ❌ IbInputSimulator_诊断工具.ahk
- ❌ POE2_血量检测器.ahk
- ❌ 按键测试-长按版本.ahk
- ❌ 检查输入设备.ahk
- ❌ 血量.ahk
- ❌ 输入测试诊断.ahk
- ❌ 新建文本文档.txt

## 📊 项目统计

- **总文件数**: 11个文件
- **脚本文件**: 3个 (.ahk)
- **文档文件**: 5个 (.md)
- **库文件**: 1个文件夹 (IbInputSimulator/)
- **代码行数**: ~1000+ 行
- **文档字数**: ~20000+ 字

## 🎯 项目目标

通过 AutoHotkey + IbInputSimulator + Logitech G HUB + reWASD 的完整解决方案，实现：
- ✅ 硬件级按键发送
- ✅ reWASD 识别和重映射
- ✅ 游戏输入限制绕过
- ✅ 稳定的自动化功能

---

**更新日期**: 2024年12月  
**项目状态**: ✅ 完整可用  
**维护状态**: 🟢 活跃维护
