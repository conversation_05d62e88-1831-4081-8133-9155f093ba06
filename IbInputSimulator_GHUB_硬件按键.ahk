#Requires AutoHotkey v2.0 64-bit
#SingleInstance Force

; ===================================================================
; IbInputSimulator GHUB 硬件按键发送器
; 专门用于通过 Logitech G HUB 驱动发送硬件按键，供 reWASD 识别和重映射
; ===================================================================

; 包含 IbInputSimulator 库
#Include "IbInputSimulator\IbInputSimulator.ahk"

; ===================================================================
; 全局变量
; ===================================================================
global ghubInitialized := false
global isRunning := false
global currentKey := "1"
global sendInterval := 880  ; 默认间隔 880ms
global keyHoldTime := 80    ; 按键持续时间 80ms
global mainGui := ""        ; 主GUI对象

; ===================================================================
; 创建GUI界面
; ===================================================================
CreateGUI() {
    ; 创建主窗口
    myGui := Gui("+Resize +MinSize400x300", "IbInputSimulator GHUB 硬件按键发送器 v1.0")
    myGui.SetFont("s10", "Microsoft YaHei")

    ; 状态显示区域
    myGui.AddText("x10 y10 w380 h30 Center", "🎮 Logitech G HUB 硬件按键发送器").SetFont("s12 Bold")

    ; 驱动状态
    myGui.AddText("x10 y50 w100", "驱动状态:")
    statusLabel := myGui.AddText("x120 y50 w260 cRed", "● 未初始化")

    ; 分隔线
    myGui.AddText("x10 y80 w380 h2 0x10")

    ; 控制按钮区域
    myGui.AddText("x10 y90 w380 h20 Center", "控制面板").SetFont("s11 Bold")

    ; 初始化按钮
    initBtn := myGui.AddButton("x10 y120 w120 h35", "🔧 初始化 GHUB")
    initBtn.OnEvent("Click", InitializeGHUB)

    ; 测试按钮
    testBtn := myGui.AddButton("x140 y120 w120 h35", "🧪 测试按键 (F2)")
    testBtn.OnEvent("Click", TestKeyInput)

    ; 启动/停止按钮
    toggleBtn := myGui.AddButton("x270 y120 w120 h35", "▶️ 启动宏 (F1)")
    toggleBtn.OnEvent("Click", ToggleMacro)
    
    ; 按键设置区域
    myGui.AddText("x10 y170 w380 h20 Center", "按键设置").SetFont("s11 Bold")

    ; 当前按键设置
    myGui.AddText("x10 y200 w80", "发送按键:")
    keyEdit := myGui.AddEdit("x100 y198 w50 h23", currentKey)
    keyEdit.OnEvent("Change", UpdateCurrentKey)

    ; 间隔设置
    myGui.AddText("x160 y200 w80", "间隔(ms):")
    intervalEdit := myGui.AddEdit("x250 y198 w60 h23", sendInterval)
    intervalEdit.OnEvent("Change", UpdateSendInterval)

    ; 持续时间设置
    myGui.AddText("x320 y200 w80", "持续(ms):")
    holdEdit := myGui.AddEdit("x380 y198 w50 h23", keyHoldTime)
    holdEdit.OnEvent("Change", UpdateKeyHoldTime)

    ; 信息显示区域
    myGui.AddText("x10 y240 w380 h20 Center", "状态信息").SetFont("s11 Bold")

    ; 状态信息文本框
    infoText := myGui.AddEdit("x10 y270 w380 h80 ReadOnly VScroll")
    infoText.Text := "等待初始化 Logitech G HUB 驱动..."

    ; 快捷键说明
    myGui.AddText("x10 y360 w380 h40",
        "快捷键: F1=启动/停止宏 | F2=测试按键 | F3=重新初始化 | F4=状态 | F5=重置 | F12=退出")

    ; 存储控件引用
    myGui.statusLabel := statusLabel
    myGui.toggleBtn := toggleBtn
    myGui.infoText := infoText
    myGui.keyEdit := keyEdit
    myGui.intervalEdit := intervalEdit
    myGui.holdEdit := holdEdit

    ; 设置关闭事件
    myGui.OnEvent("Close", (*) => ExitApp())

    return myGui
}

; ===================================================================
; 初始化 Logitech G HUB 驱动
; ===================================================================
InitializeGHUB(*) {
    global ghubInitialized, mainGui
    
    AddInfo("正在初始化 Logitech G HUB 驱动...")
    
    try {
        ; 使用 LogitechGHubNew 驱动，模式1（接管AHK输入）
        IbSendInit("LogitechGHubNew", 1)
        ghubInitialized := true
        
        ; 更新状态显示
        mainGui.statusLabel.Text := "● Logitech G HUB 已初始化"
        mainGui.statusLabel.Opt("cGreen")
        
        AddInfo("✅ Logitech G HUB 驱动初始化成功！")
        AddInfo("📋 驱动信息: LogitechGHubNew (硬件级输入)")
        AddInfo("🎯 reWASD 现在可以识别并重映射这些按键")
        
        ; 同步按键状态
        try {
            IbSyncKeyStates()
            AddInfo("🔄 按键状态已同步")
        } catch {
            AddInfo("⚠️ 按键状态同步失败（可忽略）")
        }
        
        TrayTip("GHUB 驱动", "Logitech G HUB 驱动初始化成功", 2)
        
    } catch Error as e {
        AddInfo("❌ Logitech G HUB 驱动初始化失败: " . e.Message)
        AddInfo("💡 请确保:")
        AddInfo("   1. 以管理员身份运行此脚本")
        AddInfo("   2. 已安装 Logitech G HUB 软件")
        AddInfo("   3. G HUB 软件正在运行")
        
        MsgBox("Logitech G HUB 驱动初始化失败！`n`n" .
               "错误信息: " . e.Message . "`n`n" .
               "请确保:`n" .
               "1. 以管理员身份运行此脚本`n" .
               "2. 已安装 Logitech G HUB 软件`n" .
               "3. G HUB 软件正在运行", "初始化失败", 16)
    }
}

; ===================================================================
; 测试按键输入
; ===================================================================
TestKeyInput(*) {
    global ghubInitialized, currentKey
    
    if (!ghubInitialized) {
        MsgBox("请先初始化 Logitech G HUB 驱动！", "错误", 16)
        return
    }
    
    AddInfo("🧪 开始测试按键输入...")
    AddInfo("📝 将在3秒后发送测试按键: " . currentKey)
    AddInfo("💡 请切换到记事本或其他文本编辑器查看效果")
    
    ; 倒计时
    Loop 3 {
        AddInfo("⏰ " . (4-A_Index) . " 秒后开始测试...")
        Sleep(1000)
    }
    
    try {
        ; 发送测试按键序列
        AddInfo("📤 发送测试序列: " . currentKey . " (硬件级输入)")
        
        ; 使用 IbSend 发送按键（通过 GHUB 驱动）
        IbSend(currentKey)
        Sleep(100)
        IbSend(currentKey)
        Sleep(100)
        IbSend(currentKey)
        
        AddInfo("✅ 测试完成！如果在目标程序中看到按键输出，说明 GHUB 驱动工作正常")
        AddInfo("🎯 reWASD 应该能够识别并重映射这些按键")
        
    } catch Error as e {
        AddInfo("❌ 测试失败: " . e.Message)
        MsgBox("按键测试失败！`n`n错误信息: " . e.Message, "测试失败", 16)
    }
}

; ===================================================================
; 启动/停止宏
; ===================================================================
ToggleMacro(*) {
    global isRunning, ghubInitialized, mainGui
    
    if (!ghubInitialized) {
        MsgBox("请先初始化 Logitech G HUB 驱动！", "错误", 16)
        return
    }
    
    if (!isRunning) {
        ; 启动宏
        isRunning := true
        mainGui.toggleBtn.Text := "⏹️ 停止宏 (F1)"
        mainGui.toggleBtn.Opt("cRed")
        
        AddInfo("▶️ 宏已启动 - 开始发送硬件按键")
        AddInfo("📋 按键: " . currentKey . " | 间隔: " . sendInterval . "ms | 持续: " . keyHoldTime . "ms")
        AddInfo("🎯 reWASD 将接收并重映射这些按键")
        
        ; 启动定时器
        SetTimer(MacroLoop, sendInterval)
        
        TrayTip("宏状态", "硬件按键宏已启动", 1)
        
    } else {
        ; 停止宏
        isRunning := false
        mainGui.toggleBtn.Text := "▶️ 启动宏 (F1)"
        mainGui.toggleBtn.Opt("cGreen")
        
        ; 停止定时器
        SetTimer(MacroLoop, 0)
        
        AddInfo("⏹️ 宏已停止")
        TrayTip("宏状态", "硬件按键宏已停止", 1)
    }
}

; ===================================================================
; 宏循环函数
; ===================================================================
MacroLoop() {
    global isRunning, ghubInitialized, currentKey, keyHoldTime
    
    if (!isRunning || !ghubInitialized) {
        return
    }
    
    try {
        ; 发送按键按下
        IbSend("{" . currentKey . " down}")
        
        ; 持续按键时间
        Sleep(keyHoldTime)
        
        ; 发送按键释放
        IbSend("{" . currentKey . " up}")
        
    } catch Error as e {
        ; 出错时停止宏
        ToggleMacro()
        AddInfo("❌ 宏执行出错，已自动停止: " . e.Message)
        MsgBox("宏执行出错！`n`n错误信息: " . e.Message . "`n`n宏已自动停止。", "执行错误", 16)
    }
}

; ===================================================================
; 添加信息到状态显示
; ===================================================================
AddInfo(text) {
    global mainGui

    ; 获取当前时间
    currentTime := FormatTime(, "HH:mm:ss")

    ; 添加时间戳
    newText := "[" . currentTime . "] " . text . "`r`n"
    
    ; 添加到信息框
    mainGui.infoText.Text .= newText
    
    ; 滚动到底部
    mainGui.infoText.Focus()
    Send("^{End}")
}

; ===================================================================
; GUI 事件回调函数
; ===================================================================
UpdateCurrentKey(ctrl, *) {
    global currentKey
    currentKey := ctrl.Text
}

UpdateSendInterval(ctrl, *) {
    global sendInterval
    try {
        sendInterval := Integer(ctrl.Text)
    } catch {
        sendInterval := 880  ; 默认值
    }
}

UpdateKeyHoldTime(ctrl, *) {
    global keyHoldTime
    try {
        keyHoldTime := Integer(ctrl.Text)
    } catch {
        keyHoldTime := 80  ; 默认值
    }
}

; ===================================================================
; 显示状态信息
; ===================================================================
ShowStatus(*) {
    global ghubInitialized, isRunning, currentKey, sendInterval, keyHoldTime

    statusText := "🎮 IbInputSimulator GHUB 硬件按键发送器状态`n`n"
    statusText .= "系统状态:`n"
    statusText .= "• GHUB 驱动: " . (ghubInitialized ? "✅ 已初始化" : "❌ 未初始化") . "`n"
    statusText .= "• 宏运行状态: " . (isRunning ? "🟢 运行中" : "🔴 已停止") . "`n`n"

    statusText .= "当前设置:`n"
    statusText .= "• 发送按键: " . currentKey . "`n"
    statusText .= "• 发送间隔: " . sendInterval . " ms`n"
    statusText .= "• 按键持续时间: " . keyHoldTime . " ms`n`n"

    statusText .= "快捷键:`n"
    statusText .= "• F1: 启动/停止宏`n"
    statusText .= "• F2: 测试按键输入`n"
    statusText .= "• F3: 重新初始化驱动`n"
    statusText .= "• F4: 显示状态信息`n"
    statusText .= "• F5: 重置设置`n"
    statusText .= "• F12: 退出程序`n`n"

    statusText .= "reWASD 配置建议:`n"
    statusText .= "• 在 reWASD 中选择 'Logitech Virtual G-series Keyboard'`n"
    statusText .= "• 将键盘按键映射为控制器按键`n"
    statusText .= "• 例如: 键盘 '" . currentKey . "' → 控制器 'RT'"

    MsgBox(statusText, "系统状态", 64)
}

; ===================================================================
; 重置设置
; ===================================================================
ResetSettings(*) {
    global currentKey, sendInterval, keyHoldTime, mainGui

    result := MsgBox("确定要重置所有设置为默认值吗？", "重置设置", 4)
    if (result = "Yes") {
        ; 重置为默认值
        currentKey := "1"
        sendInterval := 880
        keyHoldTime := 80

        ; 更新界面
        mainGui.keyEdit.Text := currentKey
        mainGui.intervalEdit.Text := sendInterval
        mainGui.holdEdit.Text := keyHoldTime

        AddInfo("⚙️ 设置已重置为默认值")
        AddInfo("📋 按键: " . currentKey . " | 间隔: " . sendInterval . "ms | 持续: " . keyHoldTime . "ms")

        TrayTip("设置重置", "所有设置已重置为默认值", 2)
    }
}

; ===================================================================
; 快捷键设置
; ===================================================================
F1::ToggleMacro()
F2::TestKeyInput()
F3::InitializeGHUB()
F4::ShowStatus()        ; 显示状态信息
F5::ResetSettings()     ; 重置设置
F12::ExitApp()

; ===================================================================
; 程序入口
; ===================================================================
; 检查管理员权限
if (!A_IsAdmin) {
    MsgBox("此程序需要管理员权限才能正常工作！`n`n请右键点击脚本文件，选择'以管理员身份运行'。", "需要管理员权限", 48)
    ExitApp()
}

; 创建并显示GUI
mainGui := CreateGUI()
mainGui.Show("w400 h410")

; 显示启动信息
AddInfo("🚀 IbInputSimulator GHUB 硬件按键发送器已启动")
AddInfo("📋 专为 reWASD 重映射设计")
AddInfo("💡 请点击'初始化 GHUB'按钮开始使用")

; 设置托盘图标和菜单
A_IconTip := "IbInputSimulator GHUB 硬件按键发送器"
