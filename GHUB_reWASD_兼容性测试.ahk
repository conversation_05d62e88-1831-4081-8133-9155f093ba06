#Requires AutoHotkey v2.0 64-bit
#SingleInstance Force

; ===================================================================
; GHUB + reWASD 兼容性测试工具
; 专门测试 IbInputSimulator 的 GHUB 驱动与 reWASD 的兼容性
; ===================================================================

; 包含 IbInputSimulator 库
#Include "IbInputSimulator\IbInputSimulator.ahk"

; ===================================================================
; 全局变量
; ===================================================================
global testResults := ""
global ghubInitialized := false

; ===================================================================
; 主测试函数
; ===================================================================
RunCompatibilityTest() {
    testResults := ""
    
    AddResult("🚀 开始 GHUB + reWASD 兼容性测试")
    AddResult("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    
    ; 1. 检查管理员权限
    TestAdminRights()
    
    ; 2. 检查 IbInputSimulator 库
    TestIbInputSimulator()
    
    ; 3. 测试 GHUB 驱动初始化
    TestGHUBDriver()
    
    ; 4. 测试硬件按键发送
    TestHardwareKeyInput()
    
    ; 5. 检查虚拟设备
    TestVirtualDevice()
    
    ; 6. reWASD 兼容性指导
    ProvideReWASDGuidance()
    
    AddResult("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    AddResult("🎉 兼容性测试完成！")
    
    ; 显示结果
    ShowResults()
}

; ===================================================================
; 测试管理员权限
; ===================================================================
TestAdminRights() {
    AddResult("")
    AddResult("🔐 检查管理员权限...")
    
    if (A_IsAdmin) {
        AddResult("✅ 管理员权限: 正常")
    } else {
        AddResult("❌ 管理员权限: 缺失")
        AddResult("💡 解决方案: 右键脚本选择'以管理员身份运行'")
    }
}

; ===================================================================
; 测试 IbInputSimulator 库
; ===================================================================
TestIbInputSimulator() {
    AddResult("")
    AddResult("📚 检查 IbInputSimulator 库...")
    
    ; 检查 DLL 文件
    if (FileExist("IbInputSimulator\IbInputSimulator.dll")) {
        AddResult("✅ IbInputSimulator.dll: 存在")
    } else {
        AddResult("❌ IbInputSimulator.dll: 缺失")
        AddResult("💡 解决方案: 确保 IbInputSimulator 文件夹完整")
        return
    }
    
    ; 检查 AHK 文件
    if (FileExist("IbInputSimulator\IbInputSimulator.ahk")) {
        AddResult("✅ IbInputSimulator.ahk: 存在")
    } else {
        AddResult("❌ IbInputSimulator.ahk: 缺失")
        AddResult("💡 解决方案: 确保 IbInputSimulator 文件夹完整")
        return
    }
    
    ; 测试函数可用性
    try {
        ; 尝试调用一个简单的函数来验证库是否正常加载
        DllCall("GetModuleHandle", "Str", "IbInputSimulator.dll", "Ptr")
        AddResult("✅ 库函数: 可用")
    } catch {
        AddResult("❌ 库函数: 不可用")
        AddResult("💡 解决方案: 重新下载 IbInputSimulator 库")
    }
}

; ===================================================================
; 测试 GHUB 驱动
; ===================================================================
TestGHUBDriver() {
    global ghubInitialized
    
    AddResult("")
    AddResult("🎮 测试 Logitech G HUB 驱动...")
    
    ; 检查 G HUB 进程
    if (ProcessExist("lghub.exe") || ProcessExist("LGHubUpdater.exe")) {
        AddResult("✅ G HUB 进程: 运行中")
    } else {
        AddResult("⚠️ G HUB 进程: 未检测到")
        AddResult("💡 建议: 启动 Logitech G HUB 软件")
    }
    
    ; 尝试初始化 GHUB 驱动
    try {
        IbSendInit("LogitechGHubNew", 0)  ; 模式0，不接管AHK输入
        ghubInitialized := true
        AddResult("✅ GHUB 驱动初始化: 成功")
        AddResult("📋 驱动类型: LogitechGHubNew (硬件级)")
        AddResult("🎯 reWASD 兼容性: 优秀")
        
        ; 同步按键状态
        try {
            IbSyncKeyStates()
            AddResult("✅ 按键状态同步: 成功")
        } catch {
            AddResult("⚠️ 按键状态同步: 失败（可忽略）")
        }
        
    } catch Error as e {
        AddResult("❌ GHUB 驱动初始化: 失败")
        AddResult("📝 错误信息: " . e.Message)
        AddResult("💡 解决方案:")
        AddResult("   1. 安装 Logitech G HUB 软件")
        AddResult("   2. 确保 G HUB 正在运行")
        AddResult("   3. 以管理员身份运行此脚本")
        AddResult("   4. 重启计算机后再试")
    }
}

; ===================================================================
; 测试硬件按键输入
; ===================================================================
TestHardwareKeyInput() {
    global ghubInitialized
    
    AddResult("")
    AddResult("⌨️ 测试硬件按键输入...")
    
    if (!ghubInitialized) {
        AddResult("❌ 无法测试: GHUB 驱动未初始化")
        return
    }
    
    AddResult("📝 准备发送测试按键...")
    AddResult("💡 请在3秒内切换到记事本或文本编辑器")
    
    ; 显示当前结果，让用户有时间切换窗口
    ShowResults()
    
    ; 倒计时
    Loop 3 {
        Sleep(1000)
        AddResult("⏰ " . (4-A_Index) . " 秒...")
        ShowResults()
    }
    
    try {
        ; 切换到模式1（接管AHK输入）进行测试
        IbSendMode(1)
        
        ; 发送测试序列
        AddResult("📤 发送测试序列: T-E-S-T")
        IbSend("T")
        Sleep(200)
        IbSend("E")
        Sleep(200)
        IbSend("S")
        Sleep(200)
        IbSend("T")
        
        ; 恢复模式0
        IbSendMode(0)
        
        AddResult("✅ 硬件按键测试: 完成")
        AddResult("🎯 如果在文本编辑器中看到 'TEST'，说明硬件输入正常")
        AddResult("📋 这些按键可以被 reWASD 识别和重映射")
        
    } catch Error as e {
        AddResult("❌ 硬件按键测试: 失败")
        AddResult("📝 错误信息: " . e.Message)
    }
}

; ===================================================================
; 测试虚拟设备
; ===================================================================
TestVirtualDevice() {
    AddResult("")
    AddResult("🖥️ 检查虚拟设备信息...")
    
    AddResult("📋 预期的虚拟设备:")
    AddResult("   • Logitech Virtual G-series Keyboard")
    AddResult("   • Logitech Virtual G-series Mouse")
    AddResult("")
    AddResult("🔍 设备检查方法:")
    AddResult("   1. 打开设备管理器")
    AddResult("   2. 展开'键盘'和'鼠标和其他指针设备'")
    AddResult("   3. 查找 Logitech Virtual 设备")
    AddResult("")
    AddResult("💡 如果找不到虚拟设备:")
    AddResult("   • 重新安装 Logitech G HUB")
    AddResult("   • 重启计算机")
    AddResult("   • 检查 G HUB 版本是否支持虚拟设备")
}

; ===================================================================
; reWASD 配置指导
; ===================================================================
ProvideReWASDGuidance() {
    AddResult("")
    AddResult("🎮 reWASD 配置指导...")
    AddResult("")
    AddResult("📋 配置步骤:")
    AddResult("   1. 打开 reWASD 软件")
    AddResult("   2. 在设备列表中查找 'Logitech Virtual G-series Keyboard'")
    AddResult("   3. 选择该设备并创建新配置")
    AddResult("   4. 设置按键映射（例如：键盘1 → 控制器RT）")
    AddResult("   5. 激活配置")
    AddResult("")
    AddResult("🧪 测试映射:")
    AddResult("   1. 运行 'IbInputSimulator_GHUB_硬件按键.ahk'")
    AddResult("   2. 初始化 GHUB 驱动")
    AddResult("   3. 启动宏发送按键")
    AddResult("   4. 在支持控制器的游戏中验证映射")
    AddResult("")
    AddResult("⚠️ 注意事项:")
    AddResult("   • 确保 reWASD 配置已激活")
    AddResult("   • 检查按键名称匹配")
    AddResult("   • 测试单个按键映射")
    AddResult("   • 调整响应延迟设置")
}

; ===================================================================
; 添加测试结果
; ===================================================================
AddResult(text) {
    global testResults
    testResults .= text . "`n"
}

; ===================================================================
; 显示测试结果
; ===================================================================
ShowResults() {
    global testResults

    ; 创建结果窗口
    local resultGui := Gui("+Resize", "GHUB + reWASD 兼容性测试结果")
    resultGui.SetFont("s9", "Consolas")
    
    ; 添加文本框显示结果
    local resultText := resultGui.AddEdit("x10 y10 w600 h400 ReadOnly VScroll", testResults)
    
    ; 添加按钮
    resultGui.AddButton("x10 y420 w100 h30", "复制结果").OnEvent("Click", CopyResults)
    resultGui.AddButton("x120 y420 w100 h30", "保存结果").OnEvent("Click", (*) => SaveResults())
    resultGui.AddButton("x500 y420 w100 h30", "关闭").OnEvent("Click", (*) => resultGui.Close())
    
    ; 显示窗口
    resultGui.Show("w620 h460")
}

; ===================================================================
; 复制测试结果
; ===================================================================
CopyResults(*) {
    global testResults
    A_Clipboard := testResults
    MsgBox("测试结果已复制到剪贴板！", "复制成功", 64)
}

; ===================================================================
; 保存测试结果
; ===================================================================
SaveResults() {
    global testResults

    ; 生成文件名
    local fileName := "GHUB_reWASD_测试结果_" . FormatTime(, "yyyyMMdd_HHmmss") . ".txt"

    try {
        ; 保存到文件
        FileAppend(testResults, fileName, "UTF-8")
        MsgBox("测试结果已保存到: " . fileName, "保存成功", 64)
    } catch Error as e {
        MsgBox("保存失败: " . e.Message, "保存错误", 16)
    }
}

; ===================================================================
; 清理资源
; ===================================================================
CleanUp() {
    global ghubInitialized
    
    if (ghubInitialized) {
        try {
            IbSendDestroy()
        } catch {
            ; 忽略清理错误
        }
    }
}

; ===================================================================
; 程序入口
; ===================================================================
; 设置退出时清理
OnExit(CleanUp)

; 显示启动信息
MsgBox("🚀 GHUB + reWASD 兼容性测试工具`n`n" .
       "此工具将测试:`n" .
       "• IbInputSimulator 库状态`n" .
       "• Logitech G HUB 驱动兼容性`n" .
       "• 硬件按键输入功能`n" .
       "• reWASD 配置指导`n`n" .
       "点击确定开始测试...", "兼容性测试", 64)

; 运行测试
RunCompatibilityTest()
