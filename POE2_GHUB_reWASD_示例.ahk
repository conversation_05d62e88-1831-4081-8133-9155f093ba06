#Requires AutoHotkey v2.0 64-bit
#SingleInstance Force

; ===================================================================
; POE2 + GHUB + reWASD 自动化示例
; 演示如何使用 IbInputSimulator 的 GHUB 驱动配合 reWASD 实现游戏自动化
; ===================================================================

; 包含 IbInputSimulator 库
#Include "IbInputSimulator\IbInputSimulator.ahk"

; ===================================================================
; 配置参数
; ===================================================================
global CONFIG := {
    ; 技能按键配置（这些按键将被 reWASD 重映射为控制器按键）
    SKILL_1: "1",           ; 主技能 → reWASD映射为控制器RT
    SKILL_2: "2",           ; 副技能 → reWASD映射为控制器RB  
    POTION: "3",            ; 药水 → reWASD映射为控制器X
    DODGE: "Space",         ; 闪避 → reWASD映射为控制器B
    
    ; 时间配置
    SKILL_INTERVAL: 880,    ; 主技能循环间隔(ms)
    KEY_HOLD_TIME: 80,      ; 按键持续时间(ms)
    POTION_CHECK_INTERVAL: 2000,  ; 药水检查间隔(ms)
    
    ; 血量检测配置（可选）
    HEALTH_CHECK_ENABLED: false,
    LOW_HEALTH_THRESHOLD: 0.3,  ; 30%血量时使用药水
}

; ===================================================================
; 全局状态
; ===================================================================
global STATE := {
    ghubReady: false,
    skillMacroRunning: false,
    potionMacroRunning: false,
    lastPotionTime: 0
}

; ===================================================================
; 初始化系统
; ===================================================================
InitializeSystem() {
    ; 检查管理员权限
    if (!A_IsAdmin) {
        MsgBox("需要管理员权限！`n请右键选择'以管理员身份运行'", "权限错误", 16)
        ExitApp()
    }
    
    ; 初始化 GHUB 驱动
    try {
        IbSendInit("LogitechGHubNew", 1)
        STATE.ghubReady := true
        
        ; 同步按键状态
        IbSyncKeyStates()
        
        TrayTip("系统就绪", "GHUB 驱动初始化成功，reWASD 可以识别按键", 3)
        
        return true
        
    } catch Error as e {
        MsgBox("GHUB 驱动初始化失败！`n`n" .
               "错误: " . e.Message . "`n`n" .
               "请确保:`n" .
               "1. 已安装 Logitech G HUB`n" .
               "2. G HUB 正在运行`n" .
               "3. 以管理员身份运行此脚本", "初始化失败", 16)
        return false
    }
}

; ===================================================================
; 发送硬件按键（供 reWASD 重映射）
; ===================================================================
SendHardwareKey(key, holdTime := 80) {
    if (!STATE.ghubReady) {
        return false
    }
    
    try {
        ; 发送按键按下
        IbSend("{" . key . " down}")
        
        ; 持续时间
        Sleep(holdTime)
        
        ; 发送按键释放
        IbSend("{" . key . " up}")
        
        return true
        
    } catch Error as e {
        TrayTip("按键错误", "发送按键失败: " . e.Message, 2)
        return false
    }
}

; ===================================================================
; 技能循环宏
; ===================================================================
SkillMacroLoop() {
    if (!STATE.skillMacroRunning || !STATE.ghubReady) {
        return
    }
    
    ; 发送主技能按键（将被 reWASD 映射为控制器 RT）
    SendHardwareKey(CONFIG.SKILL_1, CONFIG.KEY_HOLD_TIME)
}

; ===================================================================
; 药水检查宏
; ===================================================================
PotionMacroLoop() {
    if (!STATE.potionMacroRunning || !STATE.ghubReady) {
        return
    }
    
    currentTime := A_TickCount
    
    ; 检查是否到了药水检查时间
    if (currentTime - STATE.lastPotionTime >= CONFIG.POTION_CHECK_INTERVAL) {
        
        ; 这里可以添加血量检测逻辑
        ; 目前简单地定期发送药水按键
        if (ShouldUsePotionSimple()) {
            SendHardwareKey(CONFIG.POTION, CONFIG.KEY_HOLD_TIME)
            STATE.lastPotionTime := currentTime
            TrayTip("自动药水", "已发送药水按键（reWASD将映射为控制器X）", 1)
        }
    }
}

; ===================================================================
; 简单的药水使用判断（示例）
; ===================================================================
ShouldUsePotionSimple() {
    ; 这是一个简化的示例
    ; 实际应用中可以添加血量检测、药水冷却检测等逻辑
    
    ; 每10秒使用一次药水（示例）
    static lastUse := 0
    currentTime := A_TickCount
    
    if (currentTime - lastUse >= 10000) {  ; 10秒间隔
        lastUse := currentTime
        return true
    }
    
    return false
}

; ===================================================================
; 启动/停止技能宏
; ===================================================================
ToggleSkillMacro() {
    if (!STATE.ghubReady) {
        MsgBox("请先初始化系统！", "错误", 16)
        return
    }
    
    if (!STATE.skillMacroRunning) {
        ; 启动技能宏
        STATE.skillMacroRunning := true
        SetTimer(SkillMacroLoop, CONFIG.SKILL_INTERVAL)
        TrayTip("技能宏", "已启动 - 按键将通过reWASD映射为控制器信号", 2)
    } else {
        ; 停止技能宏
        STATE.skillMacroRunning := false
        SetTimer(SkillMacroLoop, 0)
        TrayTip("技能宏", "已停止", 1)
    }
}

; ===================================================================
; 启动/停止药水宏
; ===================================================================
TogglePotionMacro() {
    if (!STATE.ghubReady) {
        MsgBox("请先初始化系统！", "错误", 16)
        return
    }
    
    if (!STATE.potionMacroRunning) {
        ; 启动药水宏
        STATE.potionMacroRunning := true
        SetTimer(PotionMacroLoop, 500)  ; 每500ms检查一次
        TrayTip("药水宏", "已启动自动药水", 2)
    } else {
        ; 停止药水宏
        STATE.potionMacroRunning := false
        SetTimer(PotionMacroLoop, 0)
        TrayTip("药水宏", "已停止", 1)
    }
}

; ===================================================================
; 手动技能释放
; ===================================================================
CastSkill1() {
    SendHardwareKey(CONFIG.SKILL_1, CONFIG.KEY_HOLD_TIME)
    TrayTip("技能1", "已发送（reWASD→控制器RT）", 1)
}

CastSkill2() {
    SendHardwareKey(CONFIG.SKILL_2, CONFIG.KEY_HOLD_TIME)
    TrayTip("技能2", "已发送（reWASD→控制器RB）", 1)
}

UsePotionManual() {
    SendHardwareKey(CONFIG.POTION, CONFIG.KEY_HOLD_TIME)
    TrayTip("药水", "已发送（reWASD→控制器X）", 1)
}

PerformDodge() {
    SendHardwareKey(CONFIG.DODGE, CONFIG.KEY_HOLD_TIME)
    TrayTip("闪避", "已发送（reWASD→控制器B）", 1)
}

; ===================================================================
; 显示状态信息
; ===================================================================
ShowStatus() {
    statusText := "🎮 POE2 + GHUB + reWASD 自动化状态`n`n"
    statusText .= "系统状态:`n"
    statusText .= "• GHUB 驱动: " . (STATE.ghubReady ? "✅ 就绪" : "❌ 未就绪") . "`n"
    statusText .= "• 技能宏: " . (STATE.skillMacroRunning ? "🟢 运行中" : "🔴 已停止") . "`n"
    statusText .= "• 药水宏: " . (STATE.potionMacroRunning ? "🟢 运行中" : "🔴 已停止") . "`n`n"
    
    statusText .= "按键映射 (reWASD配置):`n"
    statusText .= "• 键盘 '" . CONFIG.SKILL_1 . "' → 控制器 RT (主技能)`n"
    statusText .= "• 键盘 '" . CONFIG.SKILL_2 . "' → 控制器 RB (副技能)`n"
    statusText .= "• 键盘 '" . CONFIG.POTION . "' → 控制器 X (药水)`n"
    statusText .= "• 键盘 '" . CONFIG.DODGE . "' → 控制器 B (闪避)`n`n"
    
    statusText .= "快捷键:`n"
    statusText .= "• F1: 启动/停止技能宏`n"
    statusText .= "• F2: 启动/停止药水宏`n"
    statusText .= "• F3: 手动技能1`n"
    statusText .= "• F4: 手动技能2`n"
    statusText .= "• F5: 手动药水`n"
    statusText .= "• F6: 闪避`n"
    statusText .= "• F12: 退出程序"
    
    MsgBox(statusText, "系统状态", 64)
}

; ===================================================================
; 清理资源
; ===================================================================
CleanUp() {
    ; 停止所有定时器
    SetTimer(SkillMacroLoop, 0)
    SetTimer(PotionMacroLoop, 0)
    
    ; 清理 IbInputSimulator
    if (STATE.ghubReady) {
        try {
            IbSendDestroy()
        } catch {
            ; 忽略清理错误
        }
    }
}

; ===================================================================
; 快捷键绑定
; ===================================================================
F1::ToggleSkillMacro()      ; 技能宏开关
F2::TogglePotionMacro()     ; 药水宏开关
F3::CastSkill1()            ; 手动技能1
F4::CastSkill2()            ; 手动技能2
F5::UsePotionManual()       ; 手动药水
F6::PerformDodge()          ; 闪避
F9::ShowStatus()            ; 显示状态
F12::ExitApp()              ; 退出程序

; ===================================================================
; 程序入口
; ===================================================================
; 设置退出清理
OnExit(CleanUp)

; 显示启动信息
MsgBox("🚀 POE2 + GHUB + reWASD 自动化示例`n`n" .
       "此脚本演示如何:`n" .
       "• 使用 IbInputSimulator 的 GHUB 驱动发送硬件按键`n" .
       "• 让 reWASD 识别并重映射为控制器信号`n" .
       "• 实现 POE2 游戏自动化`n`n" .
       "使用前请确保:`n" .
       "1. 已安装并运行 Logitech G HUB`n" .
       "2. 已安装并配置 reWASD`n" .
       "3. 在 reWASD 中设置了按键映射`n`n" .
       "按 F9 查看详细状态和快捷键", "启动信息", 64)

; 初始化系统
if (InitializeSystem()) {
    TrayTip("系统就绪", "按 F9 查看状态和快捷键", 3)
} else {
    ExitApp()
}
