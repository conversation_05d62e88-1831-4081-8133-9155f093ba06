#Requires AutoHotkey v2.0
#SingleInstance Force

; ===================================================================
; 输入测试诊断工具
; 功能: 测试多种输入方法，诊断IbInputSimulator问题
; ===================================================================

; 尝试包含IbInputSimulator库
try {
    #Include "IbInputSimulator.AHK2\IbInputSimulator.ahk"
    ibAvailable := true
} catch {
    ibAvailable := false
}

; 全局变量
driverInitialized := false
testResults := []
isLooping := false
currentKey := 1  ; 当前要发送的按键 (1或2)

; GUI控件引用
mainWindow := ""
resultsList := ""
statusLabel := ""
loopButton := ""

; 创建主界面
CreateDiagnosticInterface()

; 热键绑定
F1::TestStandardInput()
F2::TestIbInputSimulator()
F3::InitializeIbDriver()
F4::TestAllMethods()
F5::ToggleLoopSending()
F12::ExitApplication()

; ===================================================================
; 创建诊断界面
; ===================================================================
CreateDiagnosticInterface() {
    global mainWindow, resultsList, statusLabel, loopButton
    
    ; 创建主窗口
    mainWindow := Gui("+Resize", "输入测试诊断工具")
    
    ; 标题区域
    mainWindow.AddText("x20 y20 w460 h30 Center", "🔍 输入测试诊断工具")
    mainWindow.AddText("x20 y50 w460 h20 Center cBlue", "测试不同输入方法，诊断问题")
    mainWindow.AddText("x20 y75 w460 h2 0x10")  ; 分隔线
    
    ; 状态显示
    mainWindow.AddText("x20 y90 w100 h25", "IbInputSimulator:")
    if (ibAvailable) {
        statusLabel := mainWindow.AddText("x120 y90 w200 h25 cGreen", "● 库文件已加载")
    } else {
        statusLabel := mainWindow.AddText("x120 y90 w200 h25 cRed", "● 库文件未找到")
    }
    
    ; 测试按钮区域
    mainWindow.AddText("x20 y125 w460 h2 0x10")  ; 分隔线
    mainWindow.AddText("x20 y140 w460 h20 Center", "测试方法 (请先打开记事本)")
    
    btn1 := mainWindow.AddButton("x20 y170 w90 h35", "标准输入 (F1)")
    btn1.OnEvent("Click", TestStandardInput)
    
    btn2 := mainWindow.AddButton("x120 y170 w90 h35", "IbInputSimulator (F2)")
    btn2.OnEvent("Click", TestIbInputSimulator)
    
    btn3 := mainWindow.AddButton("x220 y170 w90 h35", "初始化驱动 (F3)")
    btn3.OnEvent("Click", InitializeIbDriver)
    
    btn4 := mainWindow.AddButton("x320 y170 w90 h35", "测试所有 (F4)")
    btn4.OnEvent("Click", TestAllMethods)
    
    loopButton := mainWindow.AddButton("x420 y170 w60 h35", "循环 (F5)")
    loopButton.OnEvent("Click", ToggleLoopSending)
    
    ; 结果显示区域
    mainWindow.AddText("x20 y220 w460 h2 0x10")  ; 分隔线
    mainWindow.AddText("x20 y235 w100 h25", "测试结果:")
    resultsList := mainWindow.AddListBox("x20 y260 w460 h150")
    
    ; 说明区域
    mainWindow.AddText("x20 y425 w460 h2 0x10")  ; 分隔线
    mainWindow.AddText("x20 y440 w460 h60 Wrap", 
        "🔧 使用说明:`n" .
        "1. 先打开记事本并确保记事本窗口处于活动状态`n" .
        "2. 依次测试不同的输入方法`n" .
        "3. 观察记事本中是否出现对应的字符")
    
    btnExit := mainWindow.AddButton("x380 y510 w100 h30", "退出 (F12)")
    btnExit.OnEvent("Click", ExitApplication)
    
    ; 设置窗口事件
    mainWindow.OnEvent("Close", ExitApplication)
    
    ; 显示窗口
    mainWindow.Show("w500 h560")
    
    ; 添加初始信息
    AddResult("=== 输入测试诊断工具已启动 ===")
    AddResult("IbInputSimulator库状态: " . (ibAvailable ? "已加载" : "未找到"))
}

; ===================================================================
; 添加测试结果
; ===================================================================
AddResult(text) {
    global resultsList, testResults
    
    timestamp := FormatTime(A_Now, "HH:mm:ss")
    resultText := "[" . timestamp . "] " . text
    
    testResults.Push(resultText)
    resultsList.Add([resultText])
    
    ; 自动滚动到最新项
    resultsList.Choose(testResults.Length)
}

; ===================================================================
; 测试标准AHK输入
; ===================================================================
TestStandardInput(*) {
    AddResult("开始测试标准AHK输入...")
    
    try {
        Sleep(2000)  ; 给用户时间切换到记事本
        Send("1")
        AddResult("✅ 标准AHK输入: 发送数字1成功")
    } catch Error as e {
        AddResult("❌ 标准AHK输入: 发送失败 - " . e.Message)
    }
}

; ===================================================================
; 初始化IbInputSimulator驱动
; ===================================================================
InitializeIbDriver(*) {
    global driverInitialized, statusLabel
    
    if (!ibAvailable) {
        AddResult("❌ 无法初始化: IbInputSimulator库未加载")
        return
    }
    
    AddResult("正在初始化IbInputSimulator驱动...")
    
    ; 尝试不同的驱动程序
    drivers := [
        {name: "LogitechGHubNew", desc: "Logitech G HUB (新版)"},
        {name: "Logitech", desc: "Logitech (传统)"},
        {name: "SendInput", desc: "标准输入"}
    ]
    
    for driver in drivers {
        try {
            AddResult("尝试初始化: " . driver.desc)
            IbSendInit(driver.name)
            driverInitialized := true
            statusLabel.Text := "● " . driver.desc . " 已初始化"
            statusLabel.Opt("cGreen")
            AddResult("✅ " . driver.desc . " 初始化成功")
            return
        } catch Error as e {
            AddResult("❌ " . driver.desc . " 初始化失败: " . e.Message)
        }
    }
    
    AddResult("❌ 所有驱动程序初始化都失败")
}

; ===================================================================
; 测试IbInputSimulator输入
; ===================================================================
TestIbInputSimulator(*) {
    if (!ibAvailable) {
        AddResult("❌ 无法测试: IbInputSimulator库未加载")
        return
    }
    
    if (!driverInitialized) {
        AddResult("❌ 无法测试: 驱动程序未初始化，请先按F3初始化")
        return
    }
    
    AddResult("开始测试IbInputSimulator输入...")
    
    try {
        Sleep(2000)  ; 给用户时间切换到记事本
        IbSend("1")
        AddResult("✅ IbInputSimulator输入: 发送数字1成功")
    } catch Error as e {
        AddResult("❌ IbInputSimulator输入: 发送失败 - " . e.Message)
    }
}

; ===================================================================
; 测试所有方法
; ===================================================================
TestAllMethods(*) {
    AddResult("=== 开始全面测试 ===")
    AddResult("请确保记事本窗口处于活动状态...")
    Sleep(3000)
    
    ; 测试标准输入
    TestStandardInput()
    Sleep(1000)
    
    ; 如果IbInputSimulator可用，测试它
    if (ibAvailable) {
        if (!driverInitialized) {
            InitializeIbDriver()
            Sleep(1000)
        }
        
        if (driverInitialized) {
            TestIbInputSimulator()
        }
    }
    
    AddResult("=== 全面测试完成 ===")
}

; ===================================================================
; 切换循环发送
; ===================================================================
ToggleLoopSending(*) {
    global isLooping, loopButton, driverInitialized, currentKey
    
    if (!driverInitialized) {
        AddResult("❌ 无法启动循环: 请先初始化驱动程序 (F3)")
        return
    }
    
    if (!isLooping) {
        ; 启动循环
        isLooping := true
        currentKey := 1  ; 重置为从1开始
        loopButton.Text := "停止 (F5)"
        loopButton.Opt("cRed")
        AddResult("🔄 开始循环发送数字1和2 (每880ms切换)")
        SetTimer(LoopSendKeys, 880)
    } else {
        ; 停止循环
        isLooping := false
        loopButton.Text := "循环 (F5)"
        loopButton.Opt("cDefault")
        AddResult("⏹️ 停止循环发送")
        SetTimer(LoopSendKeys, 0)
    }
}

; ===================================================================
; 循环发送按键1和2
; ===================================================================
LoopSendKeys() {
    global isLooping, driverInitialized, currentKey
    
    if (!isLooping || !driverInitialized) {
        return
    }
    
    try {
        ; 发送当前按键
        IbSend(String(currentKey))
        AddResult("📤 发送数字: " . currentKey)
        
        ; 切换到下一个按键
        if (currentKey == 1) {
            currentKey := 2
        } else {
            currentKey := 1
        }
        
    } catch Error as e {
        AddResult("❌ 循环发送失败: " . e.Message)
        ToggleLoopSending()  ; 停止循环
    }
}

; ===================================================================
; 退出应用程序
; ===================================================================
ExitApplication(*) {
    global driverInitialized
    
    ; 清理驱动程序
    if (driverInitialized && ibAvailable) {
        try {
            IbSendDestroy()
        } catch {
            ; 忽略清理错误
        }
    }
    
    ExitApp()
} 