#NoEnv
SetBatchLines, -1
CoordMode, Pixel, Screen
CoordMode, Mouse, Screen

; 精确的血球参数计算
global HealthBall := {}
HealthBall.topX := 1769
HealthBall.topY := 763
HealthBall.bottomX := 1769  
HealthBall.bottomY := 1038
HealthBall.centerX := 1769  ; 中心X坐标
HealthBall.centerY := Round((763 + 1038) / 2)  ; 中心Y = 900.5
HealthBall.radius := Round((1038 - 763) / 2)   ; 半径 = 137.5
HealthBall.diameter := 1038 - 763  ; 直径 = 275

; POE2 血量监控器 - 80%触发版
class POE2HealthMonitor {
    __New() {
        ; 预计算所有检测点
        this.InitCheckPoints()
        
        ; 状态追踪
        this.health := 100
        this.lastHealth := 100
        this.lastPotionTime := 0
        
        ; 阈值设置（根据你的要求调整）
        this.potionThreshold := 80    ; 80%时开始喝药
        this.emergencyThreshold := 30 ; 30%紧急
        this.criticalThreshold := 15  ; 15%危急
        
        ; 药剂冷却时间（毫秒）
        this.potionCooldown := 3500   ; 3.5秒
        this.emergencyCooldown := 1000 ; 紧急情况1秒
    }
    
    InitCheckPoints() {
        ; 基于实际坐标预计算检测点
        cx := HealthBall.centerX
        cy := HealthBall.centerY
        r := HealthBall.radius
        
        ; 水平扫描线检测点
        this.scanPoints := {}
        this.scanPoints.p95 := cx + r * 0.9    ; 95%
        this.scanPoints.p90 := cx + r * 0.8    ; 90%
        this.scanPoints.p80 := cx + r * 0.6    ; 80% - 关键阈值
        this.scanPoints.p70 := cx + r * 0.4    ; 70%
        this.scanPoints.p60 := cx + r * 0.2    ; 60%
        this.scanPoints.p50 := cx               ; 50% - 中线
        this.scanPoints.p40 := cx - r * 0.2    ; 40%
        this.scanPoints.p30 := cx - r * 0.4    ; 30%
        this.scanPoints.p20 := cx - r * 0.6    ; 20%
        this.scanPoints.p10 := cx - r * 0.8    ; 10%
        
        ; 多层扫描Y坐标（提高准确性）
        this.scanYOffsets := [-20, -10, 0, 10, 20]
    }
    
    ; 主检测函数 - 优化版
    GetHealth() {
        cy := HealthBall.centerY
        
        ; 快速二分查找
        if (this.CheckRed(this.scanPoints.p50, cy)) {
            ; >50%
            if (this.CheckRed(this.scanPoints.p80, cy)) {
                ; >80%
                if (this.CheckRed(this.scanPoints.p90, cy)) {
                    return 95  ; >90%
                }
                return 85  ; 80-90%
            } else {
                ; 50-80%
                if (this.CheckRed(this.scanPoints.p60, cy)) {
                    return 65  ; 60-80%
                }
                return 55  ; 50-60%
            }
        } else {
            ; <50%
            if (this.CheckRed(this.scanPoints.p30, cy)) {
                return 40  ; 30-50%
            } else if (this.CheckRed(this.scanPoints.p20, cy)) {
                return 25  ; 20-30%
            } else if (this.CheckRed(this.scanPoints.p10, cy)) {
                return 15  ; 10-20%
            }
            ; <10%
            return this.GetPreciseLowHealth()
        }
    }
    
    ; 检测指定X坐标是否为红色
    CheckRed(x, y) {
        PixelGetColor, color, x, y, RGB
        return this.IsRedColor(color)
    }
    
    ; 多点验证（更准确）
    CheckRedMulti(x) {
        redCount := 0
        cy := HealthBall.centerY
        
        for i, offset in this.scanYOffsets {
            if (this.CheckRed(x, cy + offset))
                redCount++
        }
        
        return redCount >= 3  ; 5个点中至少3个红色
    }
    
    ; 精确检测极低血量
    GetPreciseLowHealth() {
        leftEdge := HealthBall.centerX - HealthBall.radius
        scanStart := HealthBall.centerX - HealthBall.radius * 0.9
        
        ; 2像素精度扫描
        Loop, 45 {
            x := scanStart - (A_Index - 1) * 2
            if (x <= leftEdge)
                return 1
                
            if (this.CheckRedMulti(x)) {
                width := x - leftEdge
                percent := Round(width / (HealthBall.radius * 2) * 100)
                return percent > 0 ? percent : 1
            }
        }
        return 0
    }
    
    ; 颜色判断（针对POE2血球）
    IsRedColor(color) {
        r := (color >> 16) & 0xFF
        g := (color >> 8) & 0xFF
        b := color & 0xFF
        
        ; 正常红色
        if (r > 140 && g < 70 && b < 70)
            return true
            
        ; 暗红色（低血量）
        if (r > 100 && g < 50 && b < 50 && r > g + b)
            return true
            
        return false
    }
    
    ; 监控主循环
    Monitor() {
        ; 保存旧血量
        this.lastHealth := this.health
        
        ; 获取当前血量
        this.health := this.GetHealth()
        
        ; 计算血量变化
        healthDrop := this.lastHealth - this.health
        currentTime := A_TickCount
        
        ; 根据血量执行动作
        if (this.health <= this.criticalThreshold) {
            ; 危急状态 - 15%以下
            this.OnCriticalHealth(currentTime)
        }
        else if (this.health <= this.emergencyThreshold) {
            ; 紧急状态 - 30%以下
            this.OnEmergencyHealth(currentTime)
        }
        else if (this.health <= this.potionThreshold) {
            ; 需要喝药 - 80%以下
            this.OnNeedPotion(currentTime)
        }
        
        ; 检测突然的大量伤害
        if (healthDrop > 40) {
            this.OnBigHit(currentTime)
        }
        
        ; 显示状态
        this.UpdateDisplay()
    }
    
    ; 80%血量触发
    OnNeedPotion(currentTime) {
        ; 检查冷却
        if (currentTime - this.lastPotionTime >= this.potionCooldown) {
            Send, 1  ; 使用生命药剂
            this.lastPotionTime := currentTime
            
            ; 可选：轻微提示音
            if (this.enableSound)
                SoundBeep, 800, 50
        }
    }
    
    ; 30%紧急情况
    OnEmergencyHealth(currentTime) {
        ; 更短的冷却时间
        if (currentTime - this.lastPotionTime >= this.emergencyCooldown) {
            Send, 1  ; 生命药剂
            Send, 2  ; 备用药剂
            this.lastPotionTime := currentTime
            
            if (this.enableSound) {
                SoundBeep, 1500, 100
                SoundBeep, 1500, 100
            }
        }
    }
    
    ; 15%危急情况
    OnCriticalHealth(currentTime) {
        ; 无视冷却，立即行动
        Send, 1  ; 生命药剂
        Send, 2  ; 备用药剂  
        Send, {Space}  ; 闪避技能
        
        this.lastPotionTime := currentTime
        
        if (this.enableSound) {
            SoundBeep, 2500, 200
        }
    }
    
    ; 受到大量伤害
    OnBigHit(currentTime) {
        ; 掉血超过40%，立即反应
        if (currentTime - this.lastPotionTime >= 500) {  ; 0.5秒冷却
            Send, 1
            this.lastPotionTime := currentTime
        }
    }
    
    ; 更新显示
    UpdateDisplay() {
        if (this.showInfo) {
            ; 根据血量选择颜色
            color := this.health > 80 ? "00FF00"    ; 绿色
                  : this.health > 50 ? "FFFF00"     ; 黄色
                  : this.health > 30 ? "FFA500"     ; 橙色
                  : "FF0000"                         ; 红色
            
            ; 显示信息
            info := "血量: " . this.health . "%"
            if (this.health <= this.potionThreshold) {
                cooldown := (this.potionCooldown - (A_TickCount - this.lastPotionTime)) / 1000
                if (cooldown > 0)
                    info .= "`n药剂冷却: " . Round(cooldown, 1) . "秒"
            }
            
            ToolTip, %info%, HealthBall.centerX + 150, HealthBall.centerY, 1
        }
    }
}

; 创建监控实例
global monitor := new POE2HealthMonitor()
monitor.enableSound := false  ; 是否启用声音提醒
monitor.showInfo := true      ; 是否显示信息

; 监控循环
MonitorHealth:
    monitor.Monitor()
return

; === 热键设置 ===
F1::  ; 启动监控
    SetTimer, MonitorHealth, 10  ; 100Hz 高精度
    TrayTip, POE2血量监控, 监控已启动`n80`%自动喝药, 2
return

F2::  ; 停止监控
    SetTimer, MonitorHealth, Off
    ToolTip, , , , 1
    TrayTip, POE2血量监控, 监控已停止, 2
return

F3::  ; 切换声音提醒
    monitor.enableSound := !monitor.enableSound
    TrayTip, 声音提醒, % monitor.enableSound ? "已开启" : "已关闭", 1
return

F4::  ; 显示当前状态
    health := monitor.GetHealth()
    MsgBox, 64, 血量检测, 
    (
当前血量: %health%`%
药剂阈值: 80`%
紧急阈值: 30`%
危急阈值: 15`%

血球位置:
中心: %HealthBall.centerX%, %HealthBall.centerY%
半径: %HealthBall.radius%
    ), 3
return

F5::  ; 调试 - 显示鼠标位置颜色
    MouseGetPos, mx, my
    PixelGetColor, color, mx, my, RGB
    
    r := (color >> 16) & 0xFF
    g := (color >> 8) & 0xFF
    b := color & 0xFF
    
    isRed := monitor.IsRedColor(color)
    
    ; 计算与血球的相对位置
    relX := mx - HealthBall.centerX
    relY := my - HealthBall.centerY
    distance := Sqrt(relX**2 + relY**2)
    
    ToolTip, % "坐标: " . mx . ", " . my
            . "`n相对血球: " . relX . ", " . relY  
            . "`n距离中心: " . Round(distance)
            . "`nRGB: " . r . ", " . g . ", " . b
            . "`n是否红色: " . (isRed ? "是" : "否")
            , mx + 20, my + 20
    
    SetTimer, ClearTooltip, 3000
return

ClearTooltip:
    SetTimer, ClearTooltip, Off
    ToolTip
return

; 紧急停止
Esc::
    SetTimer, MonitorHealth, Off
    ToolTip, , , , 1
    ExitApp
return