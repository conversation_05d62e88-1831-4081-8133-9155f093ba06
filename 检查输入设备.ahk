#Requires AutoHotkey v2.0
#SingleInstance Force

; ===================================================================
; 输入设备检查工具
; 功能: 显示系统中的键盘和鼠标设备，帮助验证IbInputSimulator的输出
; ===================================================================

; 全局变量
mainGui := ""
keyboardList := ""
mouseList := ""

; 创建GUI界面
CreateDeviceCheckGUI()

CreateDeviceCheckGUI() {
    global mainGui, keyboardList, mouseList
    
    ; 创建主窗口
    mainGui := Gui("+Resize", "系统输入设备检查工具")
    
    ; 标题
    mainGui.AddText("x20 y20 w460 h30 Center", "🔍 系统输入设备检查工具")
    mainGui.AddText("x20 y50 w460 h20 Center cBlue", "检查IbInputSimulator是否创建了虚拟设备")
    mainGui.AddText("x20 y75 w460 h2 0x10")  ; 分隔线
    
    ; 设备列表区域
    mainGui.AddText("x20 y90 w100 h25", "键盘设备:")
    keyboardList := mainGui.AddListBox("x20 y115 w460 h120")
    
    mainGui.AddText("x20 y250 w100 h25", "鼠标设备:")
    mouseList := mainGui.AddListBox("x20 y275 w460 h120")
    
    ; 按钮区域
    btnRefresh := mainGui.AddButton("x20 y410 w100 h30", "刷新设备列表")
    btnRefresh.OnEvent("Click", (*) => RefreshDeviceList())
    
    btnTest := mainGui.AddButton("x140 y410 w120 h30", "测试IbInputSimulator")
    btnTest.OnEvent("Click", TestIbInputSimulator)
    
    btnClose := mainGui.AddButton("x380 y410 w100 h30", "关闭")
    btnClose.OnEvent("Click", (*) => ExitApp())
    
    ; 说明文字
    mainGui.AddText("x20 y450 w460 h60 Wrap", 
        "💡 使用说明:`n" .
        "1. 点击'刷新设备列表'查看当前设备`n" .
        "2. 运行IbInputSimulator后再次刷新，观察是否出现新的虚拟设备`n" .
        "3. Logitech驱动通常会创建'Logitech Virtual'开头的设备")
    
    ; 显示窗口
    mainGui.Show("w500 h520")
    
    ; 初始加载设备列表
    RefreshDeviceList()
}

; 刷新设备列表
RefreshDeviceList() {
    global keyboardList, mouseList
    
    ; 清空现有列表
    keyboardList.Delete()
    mouseList.Delete()
    
    try {
        ; 获取键盘设备 (通过注册表)
        keyboards := GetInputDevices("HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\HID")
        for device in keyboards {
            if (InStr(device, "kbd") || InStr(device, "keyboard") || InStr(device, "Keyboard")) {
                keyboardList.Add([device])
            }
        }
        
        ; 获取鼠标设备
        mice := GetInputDevices("HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\HID")
        for device in mice {
            if (InStr(device, "mouse") || InStr(device, "Mouse") || InStr(device, "pointing")) {
                mouseList.Add([device])
            }
        }
        
        ; 添加一些常见的虚拟设备检查
        CheckVirtualDevices()
        
    } catch Error as e {
        MsgBox("获取设备列表时出错: " . e.Message, "错误", 16)
    }
}

; 检查虚拟设备
CheckVirtualDevices() {
    global keyboardList, mouseList
    
    ; 检查常见的虚拟设备
    virtualDevices := [
        "Logitech Virtual G-series Keyboard",
        "Logitech Virtual G-series Mouse", 
        "Logitech Gaming Virtual Mouse",
        "Logitech Gaming Virtual Keyboard",
        "Razer Virtual Device",
        "DD Virtual Keyboard",
        "DD Virtual Mouse"
    ]
    
    for device in virtualDevices {
        ; 这里可以添加更复杂的设备检测逻辑
        ; 目前只是示例
    }
}

; 简化的设备获取函数
GetInputDevices(regPath) {
    devices := []
    
    ; 这里应该实现注册表读取逻辑
    ; 由于AHK v2的注册表操作比较复杂，这里提供简化版本
    
    ; 添加一些示例设备
    devices.Push("标准 PS/2 键盘")
    devices.Push("HID 键盘设备")
    devices.Push("标准 PS/2 鼠标")
    devices.Push("HID 鼠标设备")
    
    return devices
}

; 测试IbInputSimulator
TestIbInputSimulator(*) {
    result := MsgBox(
        "测试IbInputSimulator设备创建:`n`n" .
        "1. 确保已安装Logitech G HUB`n" .
        "2. 运行POE2_IbInputSimulator_Macro.ahk`n" .
        "3. 初始化Logitech驱动`n" .
        "4. 返回此窗口点击'刷新设备列表'`n`n" .
        "是否现在打开设备管理器查看设备？", 
        "测试指导", 4)
    
    if (result = 6) {  ; Yes
        Run("devmgmt.msc")
    }
} 